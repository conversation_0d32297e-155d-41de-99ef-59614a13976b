import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from '../types';
import {RequestType} from './types';

interface CreateNotificationRequest {
    title: string;
    message: string;
    iconUrl?: string;
}

interface CreateNotificationResponse {
    success: boolean;
}

class CreateNotificationHandler implements RequestHandler<CreateNotificationRequest, CreateNotificationResponse> {
    getType(): RequestType {
        return RequestType.CREATE_NOTIFICATION;
    }

    async handleRequest(
        request: CreateNotificationRequest,
        sender: chrome.runtime.MessageSender
    ): Promise<CreateNotificationResponse> {
        try {
            console.log('CSFloat Monitor: Creating Windows system notification via handler', request);

            if (chrome.notifications) {
                // Use chrome.runtime.getURL to get the correct icon path
                const iconUrl = request.iconUrl ? chrome.runtime.getURL(request.iconUrl) : chrome.runtime.getURL('icons/48.png');
                console.log('CSFloat Monitor: Using icon URL:', iconUrl);

                const notificationOptions = {
                    type: 'basic' as chrome.notifications.TemplateType,
                    iconUrl: iconUrl,
                    title: request.title,
                    message: request.message
                };

                console.log('CSFloat Monitor: Notification options:', notificationOptions);

                await chrome.notifications.create(notificationOptions);
                console.log('CSFloat Monitor: Windows notification created successfully via handler');
                return { success: true };
            } else {
                console.error('CSFloat Monitor: chrome.notifications not available in handler');
                return { success: false };
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error creating notification in handler:', error);
            return { success: false };
        }
    }
}

export const CreateNotification = new CreateNotificationHandler();
