import {init} from './utils';
import '../components/market/item_row_wrapper';
import '../components/market/utility_belt';
import {gMonitorService} from '../services/monitor_service';

init('src/lib/page_scripts/market_listing.js', main);

async function main() {
    // Initialize monitor service
    await gMonitorService.initialize();

    // Set up periodic refresh using setInterval (simpler approach)
    setInterval(() => {
        handleMarketRefresh();
    }, 30000); // 30 seconds

    console.log('CSFloat Monitor: Market listing initialized with periodic refresh');
}



/**
 * Handle market refresh in page context where g_oSearchResults is available
 */
function handleMarketRefresh() {
    console.log('CSFloat Monitor: Handling periodic market refresh');

    // Check if we're on a Steam market page and the search results object exists
    if (typeof g_oSearchResults !== 'undefined' && g_oSearchResults && g_oSearchResults.GoToPage) {
        try {
            // Use Steam's native page refresh mechanism
            g_oSearchResults.GoToPage(0, true);
            console.log('CSFloat Monitor: Market data refreshed successfully');
        } catch (error) {
            console.error('CSFloat Monitor: Failed to refresh market data:', error);
        }
    } else {
        console.log('CSFloat Monitor: g_oSearchResults not available for refresh');
    }
}
