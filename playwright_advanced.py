#!/usr/bin/env python3
"""
高级 Playwright Chrome 插件启动器
支持多种配置选项和自动化测试功能

使用方法:
python playwright_advanced.py [选项]

选项:
--headless: 无头模式运行
--test: 运行自动化测试
--url URL: 指定要打开的网址
--profile PATH: 使用指定的用户配置文件
"""

import argparse
import asyncio
import json
import os
import sys
import time
from pathlib import Path
from playwright.async_api import async_playwright


class ChromeExtensionLauncher:
    def __init__(self, extension_path: str):
        self.extension_path = extension_path
        self.playwright = None
        self.context = None
        self.user_data_dir = None

    async def setup_browser(self, headless=False, user_data_dir=None):
        """设置浏览器"""
        try:
            self.playwright = await async_playwright().start()

            # 如果没有指定用户数据目录，创建全新的临时目录
            if not user_data_dir:
                import tempfile
                import uuid
                # 使用 UUID 确保目录名唯一，避免任何缓存
                unique_id = str(uuid.uuid4())[:8]
                self.user_data_dir = tempfile.mkdtemp(prefix=f"playwright_clean_{unique_id}_")
                print("🧹 创建全新的浏览器环境（无 cookies、session 等）")
            else:
                self.user_data_dir = user_data_dir

            print(f"用户数据目录: {self.user_data_dir}")

            # 浏览器启动参数（确保干净环境）
            args = [
                f"--load-extension={self.extension_path}",
                f"--disable-extensions-except={self.extension_path}",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",  # 避免被检测为自动化
                "--incognito",  # 无痕模式
                "--disable-background-timer-throttling",  # 禁用后台定时器限制
                "--disable-backgrounding-occluded-windows",  # 禁用后台窗口
                "--disable-renderer-backgrounding",  # 禁用渲染器后台
                "--disable-features=TranslateUI",  # 禁用翻译
                "--disable-ipc-flooding-protection",  # 禁用 IPC 洪水保护
                "--disable-background-networking",  # 禁用后台网络
                "--disable-sync",  # 禁用同步
                "--disable-default-apps",  # 禁用默认应用
                "--no-first-run",  # 跳过首次运行设置
                "--disable-component-update",  # 禁用组件更新
            ]

            print("正在启动浏览器...")
            # 使用 launch_persistent_context（插件必需）
            self.context = await self.playwright.chromium.launch_persistent_context(
                self.user_data_dir,
                headless=headless,
                channel="chromium",  # 使用 chromium 通道
                args=args
            )

            print("✓ 浏览器启动成功")

            # 等待插件加载
            print("等待插件加载...")
            await asyncio.sleep(2)

            # 检查 service worker（Manifest v3 插件）
            if len(self.context.service_workers) == 0:
                print("等待 service worker 启动...")
                try:
                    service_worker = await self.context.wait_for_event('serviceworker', timeout=10000)
                    print("✓ Service worker 已启动")
                except:
                    print("⚠ Service worker 未检测到，但插件可能仍然工作")
            else:
                print(f"✓ 检测到 {len(self.context.service_workers)} 个 service worker")

            return self.context

        except Exception as e:
            print(f"✗ 浏览器启动失败: {e}")
            if "Executable doesn't exist" in str(e):
                print("\n解决方案:")
                print("1. 运行: python fix_playwright.py")
                print("2. 或手动执行: playwright install")
            raise
    
    async def create_page(self):
        """创建新页面"""
        if not self.context:
            raise RuntimeError("浏览器上下文未初始化")
        return await self.context.new_page()
    
    async def test_extension_functionality(self):
        """测试插件功能"""
        print("开始测试插件功能...")
        
        page = await self.create_page()
        
        # 测试页面列表
        test_urls = [
            {
                "name": "Steam 市场 - AK47 红线",
                "url": "https://steamcommunity.com/market/listings/730/AK-47%20%7C%20Redline%20%28Field-Tested%29",
                "wait_for": ".market_listing_row",
                "description": "测试市场页面的 float 值显示"
            },
            {
                "name": "Steam 市场 - AWP 龙狙",
                "url": "https://steamcommunity.com/market/listings/730/AWP%20%7C%20Dragon%20Lore%20%28Field-Tested%29",
                "wait_for": ".market_listing_row",
                "description": "测试高价值物品的信息显示"
            }
        ]
        
        for test_case in test_urls:
            print(f"\n测试: {test_case['name']}")
            print(f"描述: {test_case['description']}")
            
            try:
                # 导航到测试页面
                await page.goto(test_case['url'], timeout=30000)
                print(f"✓ 页面加载成功: {test_case['url']}")
                
                # 等待页面元素加载
                if test_case.get('wait_for'):
                    await page.wait_for_selector(test_case['wait_for'], timeout=10000)
                    print(f"✓ 页面元素已加载: {test_case['wait_for']}")
                
                # 等待插件处理
                await asyncio.sleep(3)
                
                # 检查插件是否注入了内容
                extension_elements = await page.query_selector_all('[class*="csfloat"], [id*="csfloat"]')
                if extension_elements:
                    print(f"✓ 检测到插件元素: {len(extension_elements)} 个")
                else:
                    print("⚠ 未检测到插件元素，可能需要更长时间加载")
                
                # 截图保存
                screenshot_path = f"test_screenshot_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path)
                print(f"✓ 截图已保存: {screenshot_path}")
                
            except Exception as e:
                print(f"✗ 测试失败: {e}")
            
            await asyncio.sleep(2)  # 测试间隔
    
    async def open_url(self, url: str):
        """打开指定网址"""
        page = await self.create_page()
        
        try:
            await page.goto(url, timeout=30000)
            print(f"✓ 已打开: {url}")
            return page
        except Exception as e:
            print(f"✗ 打开失败: {e}")
            return None
    
    async def interactive_mode(self, initial_url=None):
        """交互模式"""
        page = await self.create_page()
        
        if initial_url:
            await self.open_url(initial_url)
        else:
            # 默认打开 Steam 市场
            default_url = "https://steamcommunity.com/market/search?appid=730"
            await page.goto(default_url)
            print(f"已打开默认页面: {default_url}")
        
        print("\n浏览器已启动，插件已加载")
        print("您可以在浏览器中手动测试插件功能")
        print("按 Ctrl+C 退出程序")
        
        try:
            # 保持程序运行
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n正在关闭浏览器...")
    
    async def close(self):
        """关闭浏览器"""
        if self.context:
            await self.context.close()

        if self.playwright:
            await self.playwright.stop()

        # 清理临时目录
        if self.user_data_dir and self.user_data_dir.startswith("/tmp/") or self.user_data_dir.startswith("C:\\"):
            import shutil
            try:
                shutil.rmtree(self.user_data_dir)
                print("临时文件已清理")
            except:
                print(f"临时目录清理失败: {self.user_data_dir}")


def get_extension_path():
    """获取插件路径"""
    script_dir = Path(__file__).parent.absolute()
    extension_path = script_dir / "dist"
    
    if not extension_path.exists():
        raise FileNotFoundError(f"插件目录不存在: {extension_path}")
    
    manifest_path = extension_path / "manifest.json"
    if not manifest_path.exists():
        raise FileNotFoundError(f"manifest.json 不存在: {manifest_path}")
    
    return str(extension_path)


async def main():
    parser = argparse.ArgumentParser(description="Playwright Chrome 插件启动器")
    parser.add_argument("--headless", action="store_true", help="无头模式运行")
    parser.add_argument("--test", action="store_true", help="运行自动化测试")
    parser.add_argument("--url", help="指定要打开的网址")
    parser.add_argument("--profile", help="用户配置文件目录")
    
    args = parser.parse_args()
    
    try:
        extension_path = get_extension_path()
        launcher = ChromeExtensionLauncher(extension_path)
        
        print("CSFloat Market Checker - 高级启动器")
        print("=" * 50)
        print(f"插件路径: {extension_path}")
        
        # 设置浏览器
        await launcher.setup_browser(
            headless=args.headless,
            user_data_dir=args.profile
        )
        
        if args.test:
            # 运行测试
            await launcher.test_extension_functionality()
        else:
            # 交互模式
            await launcher.interactive_mode(args.url)
        
        await launcher.close()
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
