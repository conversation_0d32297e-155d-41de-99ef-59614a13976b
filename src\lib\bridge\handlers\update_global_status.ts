import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from '../types';
import {RequestType} from './types';
import {MonitorStatus, MonitorTask} from '../../types/monitor_types';
import {gStore} from '../../storage/store';
import {StorageKey} from '../../storage/keys';

interface UpdateGlobalStatusRequest {
    statusUpdate: Partial<MonitorStatus>;
    source: string; // 'market_listing' | 'popup' | 'monitor_service'
}

interface UpdateGlobalStatusResponse {
    success: boolean;
    currentStatus?: MonitorStatus;
}

/**
 * Gets the count of active monitor tasks
 */
async function getActiveTasksCount(): Promise<number> {
    try {
        const tasks = await gStore.getWithStorage<MonitorTask[]>(
            chrome.storage.local,
            StorageKey.MONITOR_TASKS
        );

        console.log('CSFloat Monitor: Checking tasks for active count:', tasks);

        if (!tasks) {
            console.log('CSFloat Monitor: No tasks found');
            return 0;
        }

        const activeTasks = tasks.filter(task => task.isActive);
        console.log('CSFloat Monitor: Active tasks:', activeTasks);

        return activeTasks.length;
    } catch (error) {
        console.error('CSFloat Monitor: Error getting active tasks count:', error);
        return 0;
    }
}

class UpdateGlobalStatusHandler implements RequestHandler<UpdateGlobalStatusRequest, UpdateGlobalStatusResponse> {
    getType(): RequestType {
        return RequestType.UPDATE_GLOBAL_STATUS;
    }

    async handleRequest(
        request: UpdateGlobalStatusRequest,
        sender: chrome.runtime.MessageSender
    ): Promise<UpdateGlobalStatusResponse> {
        try {
            console.log('CSFloat Monitor: Updating global status from', request.source, request.statusUpdate);

            // Get current status from storage
            const currentStatus = await gStore.getWithStorage<MonitorStatus>(
                chrome.storage.local,
                StorageKey.MONITOR_STATUS
            ) || {
                isActive: false,
                lastRefresh: Date.now(),
                totalChecked: 0,
                totalTriggered: 0,
                activeTasksCount: 0,
                recentMatches: [],
                lastGlobalUpdate: Date.now()
            };

            // Merge the status update
            const updatedStatus: MonitorStatus = {
                ...currentStatus,
                ...request.statusUpdate,
                lastGlobalUpdate: Date.now()
            };

            // Handle special cases for array fields
            if (request.statusUpdate.recentMatches) {
                // Keep only the last 10 matches to prevent storage bloat
                updatedStatus.recentMatches = request.statusUpdate.recentMatches.slice(-10);
            }

            // Only recalculate activeTasksCount if not provided in the update
            // MonitorService already provides accurate activeTasksCount
            if (request.statusUpdate.activeTasksCount === undefined) {
                console.log('CSFloat Monitor: Recalculating activeTasksCount from storage');
                updatedStatus.activeTasksCount = await getActiveTasksCount();
            }

            // Always recalculate isActive based on activeTasksCount
            updatedStatus.isActive = (updatedStatus.activeTasksCount || 0) > 0;

            console.log('CSFloat Monitor: Updated status fields:', {
                source: request.source,
                activeTasksCount: updatedStatus.activeTasksCount,
                isActive: updatedStatus.isActive,
                recalculated: request.statusUpdate.activeTasksCount === undefined
            });

            // Save updated status to storage
            await gStore.setWithStorage(
                chrome.storage.local,
                StorageKey.MONITOR_STATUS,
                updatedStatus
            );

            console.log('CSFloat Monitor: Global status updated successfully', updatedStatus);

            return {
                success: true,
                currentStatus: updatedStatus
            };
        } catch (error) {
            console.error('CSFloat Monitor: Error updating global status:', error);
            return {
                success: false
            };
        }
    }
}

export const UpdateGlobalStatus = new UpdateGlobalStatusHandler();
