import {MON<PERSON><PERSON>_TASKS} from '../storage/keys';
import {MonitorTask} from '../types/monitor_types';
import {Get} from '../bridge/handlers/storage_get';
import {Set} from '../bridge/handlers/storage_set';
import {ReplaySubject} from 'rxjs';
import {debounce} from 'lodash-decorators';
import {ClientSend} from '../bridge/client';
import {UpdateGlobalStatus} from '../bridge/handlers/update_global_status';

/**
 * Provides state management for monitor tasks
 */
class MonitorService {
    private tasks: MonitorTask[] = [];
    /* Send last value upon subscription */
    private onUpdate = new ReplaySubject<MonitorTask[]>(1);
    onUpdate$ = this.onUpdate.asObservable();

    constructor() {}

    /**
     * Initializes the service by loading tasks from storage
     *
     * This should be called before any other method
     */
    async initialize() {
        try {
            console.log('CSFloat Monitor: Initializing MonitorService...');

            // Use CSFloat's storage bridge system
            const tasks = await Get(MONITOR_TASKS);
            console.log('CSFloat Monitor: Loaded tasks from storage bridge:', tasks);

            this.tasks = tasks || [];
            this.onUpdate.next(this.tasks);
            console.log('CSFloat Monitor: Initialized with', this.tasks.length, 'tasks');

            // Update global status after initialization
            await this.updateGlobalStatusFromService();
        } catch (error) {
            console.error('CSFloat Monitor: Failed to initialize:', error);
            this.tasks = [];
            this.onUpdate.next(this.tasks);
        }
    }

    /**
     * Gets all monitor tasks
     */
    getTasks(): MonitorTask[] {
        return this.tasks;
    }

    /**
     * Gets active monitor tasks only
     */
    getActiveTasks(): MonitorTask[] {
        return this.tasks.filter(task => task.isActive);
    }

    /**
     * Adds a new monitor task
     */
    async addTask(task: Omit<MonitorTask, 'id' | 'createdAt'>) {
        const newTask: MonitorTask = {
            ...task,
            id: crypto.randomUUID(),
            createdAt: Date.now()
        };
        this.tasks.push(newTask);
        await this.save();
    }

    /**
     * Removes a monitor task by ID
     */
    async removeTask(id: string) {
        this.tasks = this.tasks.filter(t => t.id !== id);
        await this.save();
    }

    /**
     * Updates a monitor task
     */
    async updateTask(id: string, updates: Partial<MonitorTask>) {
        const index = this.tasks.findIndex(t => t.id === id);
        if (index !== -1) {
            this.tasks[index] = { ...this.tasks[index], ...updates };
            await this.save();
        }
    }

    /**
     * Toggles the active state of a task
     */
    async toggleTask(id: string) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            await this.updateTask(id, { isActive: !task.isActive });
        }
    }

    /**
     * Checks if any active task matches the given conditions
     * Returns the first matching task or null
     */
    checkConditions(marketHashName: string, price: number, floatValue: number): MonitorTask | null {
        return this.tasks.find(task =>
            task.isActive &&
            task.marketHashName === marketHashName &&
            floatValue <= task.maxFloat &&
            price <= task.maxPrice &&
            task.quantity > 0
        ) || null;
    }

    /**
     * Gets tasks by market hash name
     */
    getTasksByMarketHashName(marketHashName: string): MonitorTask[] {
        return this.tasks.filter(task => task.marketHashName === marketHashName);
    }

    /**
     * Gets task by ID
     */
    getTaskById(id: string): MonitorTask | undefined {
        return this.tasks.find(task => task.id === id);
    }

    // Prevent spamming and hitting MAX_WRITE_OPERATIONS_PER_MINUTE
    @debounce(500)
    private async save() {
        await Set(MONITOR_TASKS, this.tasks);
        this.onUpdate.next(this.tasks);

        // Update global status after saving tasks
        await this.updateGlobalStatusFromService();
    }

    private async updateGlobalStatusFromService() {
        try {
            const activeTasksCount = this.getActiveTasks().length;

            await ClientSend(UpdateGlobalStatus, {
                statusUpdate: {
                    activeTasksCount: activeTasksCount,
                    lastGlobalUpdate: Date.now()
                },
                source: 'monitor_service'
            });

            console.log('CSFloat Monitor: Global status updated from MonitorService, active tasks:', activeTasksCount);
        } catch (error) {
            console.error('CSFloat Monitor: Error updating global status from MonitorService:', error);
        }
    }
}

export const gMonitorService = new MonitorService();
