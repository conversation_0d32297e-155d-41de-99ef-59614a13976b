// Import types for interface definition
interface MonitorTask {
    id: string;
    name: string;
    marketHashName: string;
    maxPrice: number;
    maxFloat: number;
    quantity: number;
    isActive: boolean;
    createdAt: number;
}

async function loadTasks(): Promise<MonitorTask[]> {
    try {
        // Try sync storage first (like CSFloat's gStore)
        let result = await chrome.storage.sync.get('monitor_tasks');
        let tasksData = result.monitor_tasks;

        // If not found in sync, try local storage
        if (!tasksData) {
            result = await chrome.storage.local.get('monitor_tasks');
            tasksData = result.monitor_tasks;
        }

        console.log('CSFloat Monitor Popup: Raw storage result:', result);
        console.log('CSFloat Monitor Popup: Tasks data:', tasksData);

        if (!tasksData) return [];

        // Handle both JSON string and direct array formats
        const tasks = typeof tasksData === 'string' ? JSON.parse(tasksData) : tasksData;
        console.log('CSFloat Monitor Popup: Parsed tasks:', tasks);
        return tasks;
    } catch (error) {
        console.error('CSFloat Monitor: Failed to load tasks:', error);
        return [];
    }
}

async function saveTasks(tasks: MonitorTask[]): Promise<void> {
    try {
        // Save in CSFloat's storage format (JSON stringified)
        const jsonData = JSON.stringify(tasks);

        // Save to both sync and local for compatibility
        const storageData = { 'monitor_tasks': jsonData };

        // Try sync storage first (preferred by CSFloat)
        try {
            await chrome.storage.sync.set(storageData);
            console.log('CSFloat Monitor Popup: Saved to sync storage');
        } catch (syncError) {
            console.log('CSFloat Monitor Popup: Sync storage failed, using local:', syncError);
            await chrome.storage.local.set(storageData);
            console.log('CSFloat Monitor Popup: Saved to local storage');
        }

        console.log('CSFloat Monitor Popup: Saved tasks:', tasks);
        console.log('CSFloat Monitor Popup: Saved as JSON:', jsonData);

        // Update global status after saving tasks
        await updateGlobalStatusFromPopup('task_save');
    } catch (error) {
        console.error('CSFloat Monitor: Failed to save tasks:', error);
    }
}

function renderTaskList(tasks: MonitorTask[]): void {
    const taskList = document.getElementById('taskList');
    if (!taskList) return;

    if (tasks.length === 0) {
        taskList.innerHTML = '<div style="color: #ccc; font-size: 12px; text-align: center; padding: 10px;">暂无监控任务</div>';
        return;
    }

    taskList.innerHTML = tasks.map(task => `
        <div class="task-item">
            <div class="task-name">${task.name}</div>
            <div class="task-details">${task.marketHashName}</div>
            <div class="task-details">价格≤${task.maxPrice} | 磨损≤${task.maxFloat} | 数量:${task.quantity}</div>
            <div class="task-buttons">
                <button data-action="toggle" data-task-id="${task.id}" class="${task.isActive ? 'btn-active' : 'btn-inactive'}">
                    ${task.isActive ? '暂停' : '启用'}
                </button>
                <button data-action="delete" data-task-id="${task.id}" class="btn-delete">删除</button>
            </div>
        </div>
    `).join('');

    // Add event delegation for task buttons
    taskList.removeEventListener('click', handleTaskButtonClick);
    taskList.addEventListener('click', handleTaskButtonClick);
}

async function handleTaskButtonClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.matches('button[data-action]')) return;

    const action = target.getAttribute('data-action');
    const taskId = target.getAttribute('data-task-id');

    if (!taskId) return;

    if (action === 'toggle') {
        await toggleTask(taskId);
    } else if (action === 'delete') {
        await deleteTask(taskId);
    }
}

async function toggleTask(id: string) {
    const tasks = await loadTasks();
    const task = tasks.find((t: MonitorTask) => t.id === id);
    if (task) {
        task.isActive = !task.isActive;
        await saveTasks(tasks);
        renderTaskList(tasks);

        // Update global status after toggling task
        await updateGlobalStatusFromPopup('task_toggle');
    }
}

async function deleteTask(id: string) {
    if (confirm('确定要删除这个监控任务吗？')) {
        const tasks = await loadTasks();
        const filteredTasks = tasks.filter((t: MonitorTask) => t.id !== id);
        await saveTasks(filteredTasks);
        renderTaskList(filteredTasks);

        // Update global status after deleting task
        await updateGlobalStatusFromPopup('task_delete');
    }
}

window.addEventListener('DOMContentLoaded', async () => {
    const requestButton = document.getElementById('requestPermissions');
    if (!requestButton) {
        return;
    }

    const hasPermissions = await chrome.permissions.contains({
        origins: ['*://*.steampowered.com/*'],
    });

    if (hasPermissions) {
        // If permissions are already granted, disable the button
        requestButton.children[1].textContent = 'Offer Tracking Enabled';
        requestButton.setAttribute('disabled', 'true');
    } else {
        requestButton.addEventListener('click', async () => {
            try {
                const success = await chrome.permissions.request({
                    origins: ['*://*.steampowered.com/*'],
                });
                if (success) {
                    // extension requires reload to apply permissions
                    browser.runtime.reload();
                }
            } catch (error) {
                console.error('Error requesting permissions:', error);
            }
        });
    }

    // Load and render monitor tasks
    const tasks = await loadTasks();
    renderTaskList(tasks);

    // Add task button event
    const addTaskBtn = document.getElementById('addTaskBtn');
    addTaskBtn?.addEventListener('click', () => {
        const modal = document.getElementById('addTaskModal');
        if (modal) modal.style.display = 'block';
    });

    // Save task event
    const saveTaskBtn = document.getElementById('saveTaskBtn');
    saveTaskBtn?.addEventListener('click', async () => {
        const nameInput = document.getElementById('taskName') as HTMLInputElement;
        const marketHashNameInput = document.getElementById('marketHashName') as HTMLInputElement;
        const maxPriceInput = document.getElementById('maxPrice') as HTMLInputElement;
        const maxFloatInput = document.getElementById('maxFloat') as HTMLInputElement;
        const quantityInput = document.getElementById('quantity') as HTMLInputElement;

        const name = nameInput?.value?.trim();
        const marketHashName = marketHashNameInput?.value?.trim();
        const maxPrice = parseInt(maxPriceInput?.value || '0');
        const maxFloat = parseFloat(maxFloatInput?.value || '0');
        const quantity = parseInt(quantityInput?.value || '0');

        if (name && marketHashName && maxPrice > 0 && maxFloat > 0 && quantity > 0) {
            const newTask: MonitorTask = {
                id: crypto.randomUUID(),
                name,
                marketHashName,
                maxPrice,
                maxFloat,
                quantity,
                isActive: true,
                createdAt: Date.now()
            };

            const currentTasks = await loadTasks();
            currentTasks.push(newTask);
            await saveTasks(currentTasks);
            renderTaskList(currentTasks);

            // Hide modal and clear form
            const modal = document.getElementById('addTaskModal');
            if (modal) modal.style.display = 'none';

            [nameInput, marketHashNameInput, maxPriceInput, maxFloatInput, quantityInput].forEach(input => {
                if (input) input.value = '';
            });
        } else {
            alert('请填写所有必填字段，并确保数值大于0');
        }
    });

    // Cancel button event
    const cancelTaskBtn = document.getElementById('cancelTaskBtn');
    cancelTaskBtn?.addEventListener('click', () => {
        const modal = document.getElementById('addTaskModal');
        if (modal) modal.style.display = 'none';
    });

    // Close modal when clicking outside
    const modal = document.getElementById('addTaskModal');
    modal?.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Load global settings
    await loadGlobalSettings();

    // Global overlay enable/disable
    const enableOverlayCheckbox = document.getElementById('enableGlobalOverlay') as HTMLInputElement;
    enableOverlayCheckbox?.addEventListener('change', async () => {
        await saveGlobalSettings({
            globalOverlayEnabled: enableOverlayCheckbox.checked
        });
    });

    // Overlay opacity slider
    const opacitySlider = document.getElementById('overlayOpacity') as HTMLInputElement;
    const opacityValue = document.getElementById('opacityValue') as HTMLSpanElement;

    opacitySlider?.addEventListener('input', () => {
        const value = parseFloat(opacitySlider.value);
        if (opacityValue) {
            opacityValue.textContent = Math.round(value * 100) + '%';
        }
    });

    opacitySlider?.addEventListener('change', async () => {
        const value = parseFloat(opacitySlider.value);
        await saveGlobalSettings({
            overlayOpacity: value
        });
    });

    // Reset overlay position button
    const resetPositionBtn = document.getElementById('resetOverlayPosition');
    resetPositionBtn?.addEventListener('click', resetOverlayPosition);
});

/**
 * Update global status from popup operations
 */
async function updateGlobalStatusFromPopup(source: string) {
    try {
        // Send message to background script to update global status
        await chrome.runtime.sendMessage({
            type: 'UPDATE_GLOBAL_STATUS_FROM_POPUP',
            source: source,
            timestamp: Date.now()
        });

        console.log('CSFloat Monitor Popup: Global status update requested from', source);
    } catch (error) {
        console.error('CSFloat Monitor Popup: Error updating global status:', error);
    }
}

/**
 * Load global settings from storage
 */
async function loadGlobalSettings() {
    try {
        const result = await chrome.storage.local.get([
            'globalOverlayEnabled',
            'overlayOpacity'
        ]);

        // Set default values
        const settings = {
            globalOverlayEnabled: result.globalOverlayEnabled !== false, // Default to true
            overlayOpacity: result.overlayOpacity || 0.95
        };

        // Update UI elements
        const enableCheckbox = document.getElementById('enableGlobalOverlay') as HTMLInputElement;
        const opacitySlider = document.getElementById('overlayOpacity') as HTMLInputElement;
        const opacityValue = document.getElementById('opacityValue') as HTMLSpanElement;

        if (enableCheckbox) {
            enableCheckbox.checked = settings.globalOverlayEnabled;
        }

        if (opacitySlider) {
            opacitySlider.value = settings.overlayOpacity.toString();
        }

        if (opacityValue) {
            opacityValue.textContent = Math.round(settings.overlayOpacity * 100) + '%';
        }

        console.log('CSFloat Monitor Popup: Global settings loaded:', settings);
    } catch (error) {
        console.error('CSFloat Monitor Popup: Error loading global settings:', error);
    }
}

/**
 * Save global settings to storage
 */
async function saveGlobalSettings(settings: any) {
    try {
        await chrome.storage.local.set(settings);
        console.log('CSFloat Monitor Popup: Global settings saved:', settings);

        // Update global status after settings change
        await updateGlobalStatusFromPopup('settings_change');
    } catch (error) {
        console.error('CSFloat Monitor Popup: Error saving global settings:', error);
    }
}

/**
 * Reset overlay position
 */
async function resetOverlayPosition() {
    try {
        await chrome.storage.local.remove(['globalOverlayPosition']);
        console.log('CSFloat Monitor Popup: Overlay position reset');

        // Show feedback to user
        const resetBtn = document.getElementById('resetOverlayPosition') as HTMLButtonElement;
        if (resetBtn) {
            const originalText = resetBtn.textContent;
            resetBtn.textContent = '已重置';
            resetBtn.disabled = true;

            setTimeout(() => {
                resetBtn.textContent = originalText;
                resetBtn.disabled = false;
            }, 2000);
        }
    } catch (error) {
        console.error('CSFloat Monitor Popup: Error resetting overlay position:', error);
    }
}
