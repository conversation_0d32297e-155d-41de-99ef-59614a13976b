{"name": "csfloat-extension", "version": "5.7.0", "description": "Dedicated API for fetching the float value, paint seed, and more of CS:GO items on the Steam Market or Inventories", "main": ".eslintrc", "directories": {"lib": "src/lib"}, "repository": {"type": "git", "url": "git+https://github.com/csfloat/extension.git"}, "scripts": {"build": "webpack --env mode=prod browser=chrome --config webpack.config.js --stats-error-details", "build_ff": "webpack --env mode=prod browser=firefox --config webpack.config.js --stats-error-details", "start": "webpack --env mode=development browser=chrome --config webpack.config.js --stats-error-details --watch", "start_ff": "webpack --env mode=development browser=firefox --config webpack.config.js --stats-error-details --watch", "lint": "eslint .", "format": "prettier --ignore-path .lint<PERSON><PERSON> --write \"**/*.+(js|ts|json)\"", "checkformat": "prettier --ignore-path .lint<PERSON>ore --check \"**/*.+(js|ts|json)\"", "generate_bluegem": "deno run --allow-net --allow-write --allow-read tools/generate_bluegem_json.ts"}, "author": "step7750", "license": "MIT", "bugs": {"url": "https://github.com/csfloat/extension/issues"}, "homepage": "https://github.com/csfloat/extension#readme", "devDependencies": {"@eslint/compat": "^1.2.8", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@types/chrome": "^0.0.313", "@types/compression-webpack-plugin": "^9.1.4", "@types/firefox-webext-browser": "^120.0.4", "@types/jquery": "^3.5.32", "@types/lodash": "^4.17.16", "@types/pako": "^2.0.3", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^13.0.0", "csgo-fade-percentage-calculator": "^1.1.6", "css-loader": "^7.1.2", "decorator-cache-getter": "^1.0.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "fast-json-stable-stringify": "^2.1.0", "file-loader": "^6.2.0", "file-replace-loader": "^1.4.2", "filtrex": "^3.1.0", "glob": "^11.0.1", "globals": "^16.0.0", "html-loader": "^5.1.0", "ignore-loader": "^0.1.2", "lit": "^3.2.1", "lit-html": "^3.2.1", "lodash": "^4.17.21", "lodash-decorators": "^6.0.1", "mini-css-extract-plugin": "^2.9.2", "pako": "^2.1.0", "prettier": "^3.5.3", "rxjs": "^7.8.2", "sass-loader": "^16.0.5", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}}