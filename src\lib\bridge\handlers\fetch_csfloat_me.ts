import {<PERSON>Hand<PERSON>} from './main';
import {RequestType} from './types';
import {environment} from '../../../environment';

export interface FetchCSFloatMeRequest {}

export interface FetchCSFloatMeResponse {
    actionable_trades: number;
    has_unread_notifications: boolean;
    pending_offers: number;
    user: {
        steam_id: string;
        username: string;
        avatar: string;
        stall_public: boolean;
        online?: boolean;
        flags: number;
        away?: boolean;
        fee: number;
    };
}

export interface FetchCSFloatMeError {
    code?: string;
    message?: string;
}

export const FetchCSFloatMe = new SimpleHandler<FetchCSFloatMeRequest, FetchCSFloatMeResponse>(
    RequestType.FETCH_CSFLOAT_ME,
    async (req) => {
        console.log('CSFloat Debug: Making request to', `${environment.csfloat_base_api_url}/v1/me`);
        console.log('CSFloat Debug: Extension ID:', chrome.runtime.id);

        return fetch(`${environment.csfloat_base_api_url}/v1/me`, {
            credentials: 'include',
        }).then((resp) => {
            console.log('CSFloat Debug: Response status:', resp.status);
            console.log('CSFloat Debug: Response headers:', Object.fromEntries(resp.headers.entries()));

            return resp.json().then((json: FetchCSFloatMeResponse | FetchCSFloatMeError) => {
                console.log('CSFloat Debug: Response body:', json);

                if (resp.ok) {
                    return json;
                } else {
                    console.error('CSFloat Debug: Error response:', json);
                    throw Error((json as FetchCSFloatMeError).message);
                }
            }) as Promise<FetchCSFloatMeResponse>;
        }).catch((error) => {
            console.error('CSFloat Debug: Fetch error:', error);
            throw error;
        });
    }
);
