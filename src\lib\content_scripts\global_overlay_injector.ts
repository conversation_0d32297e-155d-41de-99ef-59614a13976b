import '../components/global/global_overlay';
import {MonitorStatus} from '../types/monitor_types';
import {StorageKey} from '../storage/keys';

class GlobalOverlayInjector {
    private overlay: HTMLElement | null = null;
    private isInjected: boolean = false;
    private storageListener: ((changes: any, namespace: string) => void) | null = null;
    private checkTimeout: number | null = null;

    async initialize() {
        console.log('CSFloat Monitor: Initializing global overlay injector on:', window.location.href);

        // Skip initialization on certain pages to improve performance
        if (this.shouldSkipPage()) {
            console.log('CSFloat Monitor: Skipping overlay injection on this page:', window.location.href);
            return;
        }

        // Check if we should show overlay based on user settings
        const settings = await this.getGlobalSettings();
        console.log('CSFloat Monitor: Global settings loaded:', settings);
        if (!settings.enabled) {
            console.log('CSFloat Monitor: Global overlay disabled in settings');
            return;
        }

        // Set up storage listener
        this.storageListener = this.handleStorageChange.bind(this);
        chrome.storage.onChanged.addListener(this.storageListener);
        console.log('CSFloat Monitor: Storage listener set up');

        // Initial status check
        console.log('CSFloat Monitor: Performing initial status check');
        await this.checkAndInject();
    }

    private shouldSkipPage(): boolean {
        const url = window.location.href;
        const hostname = window.location.hostname;

        // Skip on non-Steam pages
        if (!hostname.includes('steamcommunity.com') && !hostname.includes('steampowered.com')) {
            return true;
        }

        // Skip on certain Steam pages that don't need overlay
        const skipPatterns = [
            '/login',
            '/logout',
            '/openid',
            '/oauth',
            '/api/',
            '/public/',
            '/help/',
            '/support/'
        ];

        return skipPatterns.some(pattern => url.includes(pattern));
    }

    private async getGlobalSettings(): Promise<{enabled: boolean}> {
        try {
            const result = await chrome.storage.local.get(['globalOverlayEnabled']);
            return {
                enabled: result.globalOverlayEnabled !== false // Default to true
            };
        } catch (error) {
            console.error('CSFloat Monitor: Error getting global settings:', error);
            return { enabled: true }; // Default to enabled
        }
    }

    private async handleStorageChange(changes: any, namespace: string) {
        if (namespace !== 'local') return;

        // Handle monitor status changes
        if (changes[StorageKey.MONITOR_STATUS]) {
            this.debouncedCheckAndInject();
        }

        // Handle settings changes
        if (changes['globalOverlayEnabled']) {
            const newEnabled = changes['globalOverlayEnabled'].newValue;
            if (!newEnabled) {
                this.removeOverlay();
            } else {
                this.debouncedCheckAndInject();
            }
        }
    }

    private debouncedCheckAndInject() {
        // Clear existing timeout
        if (this.checkTimeout) {
            clearTimeout(this.checkTimeout);
        }

        // Set new timeout
        this.checkTimeout = window.setTimeout(() => {
            this.checkAndInject();
            this.checkTimeout = null;
        }, 100); // 100ms debounce
    }

    private async checkAndInject() {
        try {
            const status = await this.getMonitorStatus();
            const settings = await this.getGlobalSettings();

            console.log('CSFloat Monitor: Checking injection conditions:', {
                url: window.location.href,
                settings: settings,
                status: status,
                isActive: status?.isActive,
                activeTasksCount: status?.activeTasksCount
            });

            // Show overlay if:
            // 1. Global overlay is enabled in settings
            // 2. Monitoring is active
            // 3. There are active tasks
            const shouldShow = settings.enabled &&
                              status?.isActive &&
                              (status?.activeTasksCount || 0) > 0;

            console.log('CSFloat Monitor: Should show overlay:', shouldShow);

            if (shouldShow) {
                this.injectOverlay();
            } else {
                this.removeOverlay();
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error in checkAndInject:', error);
        }
    }

    private async getMonitorStatus(): Promise<MonitorStatus | null> {
        try {
            const result = await chrome.storage.local.get([StorageKey.MONITOR_STATUS]);
            const statusData = result[StorageKey.MONITOR_STATUS];
            
            if (!statusData) {
                return null;
            }

            // Handle both string and object formats
            if (typeof statusData === 'string') {
                return JSON.parse(statusData);
            }
            
            return statusData;
        } catch (error) {
            console.error('CSFloat Monitor: Error getting monitor status:', error);
            return null;
        }
    }

    private async injectOverlay() {
        if (this.isInjected || this.overlay) {
            console.log('CSFloat Monitor: Overlay already injected, skipping');
            return; // Already injected
        }

        try {
            console.log('CSFloat Monitor: Attempting to inject overlay...');

            // Get current status for display
            const status = await this.getMonitorStatus();

            // Create a simple div element instead of custom element
            const overlay = document.createElement('div');
            overlay.id = 'csfloat-global-overlay';
            overlay.className = 'csfloat-global-overlay';

            // Add basic styles
            overlay.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 99999;
                background: rgba(21, 23, 28, 0.95);
                border: 3px solid #ff0000;
                border-radius: 8px;
                padding: 12px;
                min-width: 200px;
                max-width: 350px;
                font-family: 'Roboto', Arial, sans-serif;
                font-size: 12px;
                color: #ebebeb;
                user-select: none;
                cursor: move;
            `;

            // Add content
            overlay.innerHTML = `
                <div style="margin-bottom: 8px; font-weight: bold; color: #4CAF50;">
                    🎯 CSFloat Monitor Active
                </div>
                <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">
                    Active Tasks: ${status?.activeTasksCount || 0}
                </div>
                <div style="font-size: 11px; opacity: 0.8;">
                    Total Triggered: ${status?.totalTriggered || 0}
                </div>
            `;

            console.log('CSFloat Monitor: Created overlay element:', overlay);

            // Add to document body
            if (document.body) {
                document.body.appendChild(overlay);
                this.overlay = overlay;
                this.isInjected = true;
                console.log('CSFloat Monitor: Global overlay injected successfully to body');
            } else {
                console.log('CSFloat Monitor: Document body not ready, waiting...');
                // If body is not ready, wait for it
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        console.log('CSFloat Monitor: DOM loaded, retrying injection');
                        this.injectOverlay();
                    });
                }
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error injecting overlay:', error);
        }
    }

    private removeOverlay() {
        if (!this.isInjected || !this.overlay) {
            return; // Nothing to remove
        }

        try {
            if (this.overlay.parentNode) {
                this.overlay.parentNode.removeChild(this.overlay);
            }
            this.overlay = null;
            this.isInjected = false;
            console.log('CSFloat Monitor: Global overlay removed');
        } catch (error) {
            console.error('CSFloat Monitor: Error removing overlay:', error);
        }
    }

    public cleanup() {
        // Clear timeout
        if (this.checkTimeout) {
            clearTimeout(this.checkTimeout);
            this.checkTimeout = null;
        }

        // Remove storage listener
        if (this.storageListener) {
            chrome.storage.onChanged.removeListener(this.storageListener);
            this.storageListener = null;
        }

        // Remove overlay
        this.removeOverlay();
    }
}

// Initialize the injector when the script loads
let injector: GlobalOverlayInjector | null = null;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeInjector);
} else {
    initializeInjector();
}

async function initializeInjector() {
    try {
        injector = new GlobalOverlayInjector();
        await injector.initialize();
    } catch (error) {
        console.error('CSFloat Monitor: Failed to initialize global overlay injector:', error);
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (injector) {
        injector.cleanup();
    }
});

// Export for potential external access
(window as any).CSFloatGlobalOverlayInjector = injector;
