#!/usr/bin/env python3
"""
Playwright 快速修复脚本
解决浏览器二进制文件缺失问题

使用方法:
python fix_playwright.py
"""

import subprocess
import sys
import os


def run_command_with_output(command, description):
    """运行命令并实时显示输出"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    print("-" * 50)
    
    try:
        # 使用 Popen 实时显示输出
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            universal_newlines=True
        )
        
        # 实时输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"\n✓ {description} 成功")
            return True
        else:
            print(f"\n✗ {description} 失败 (返回码: {return_code})")
            return False
            
    except Exception as e:
        print(f"\n✗ {description} 异常: {e}")
        return False


def check_playwright_installation():
    """检查 Playwright 安装状态"""
    print("检查 Playwright 安装状态...")
    
    try:
        import playwright
        print(f"✓ Playwright 包已安装，版本: {playwright.__version__}")
        
        # 检查浏览器路径
        from playwright._impl._driver import compute_driver_executable
        driver_path = compute_driver_executable()
        print(f"Driver 路径: {driver_path}")
        
        return True
    except ImportError:
        print("✗ Playwright 包未安装")
        return False
    except Exception as e:
        print(f"⚠ Playwright 检查异常: {e}")
        return False


def fix_playwright():
    """修复 Playwright 安装"""
    print("Playwright 快速修复工具")
    print("=" * 50)
    
    # 检查当前状态
    check_playwright_installation()
    
    print("\n开始修复...")
    
    # 步骤 1: 重新安装 Playwright
    if not run_command_with_output(
        "pip install --force-reinstall playwright",
        "重新安装 Playwright 包"
    ):
        print("包安装失败，尝试使用国内镜像...")
        if not run_command_with_output(
            "pip install --force-reinstall playwright -i https://pypi.tuna.tsinghua.edu.cn/simple",
            "使用清华镜像重新安装 Playwright 包"
        ):
            return False
    
    # 步骤 2: 安装所有浏览器
    if not run_command_with_output(
        "playwright install",
        "安装所有浏览器"
    ):
        print("浏览器安装失败，尝试单独安装 Chromium...")
        if not run_command_with_output(
            "playwright install chromium",
            "单独安装 Chromium 浏览器"
        ):
            return False
    
    # 步骤 3: 验证安装
    print("\n验证修复结果...")
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            print("尝试启动 Chromium...")
            browser = p.chromium.launch(headless=True)
            print("✓ Chromium 启动成功")
            browser.close()
            print("✓ Chromium 关闭成功")
        
        print("\n✓ Playwright 修复成功！")
        return True
        
    except Exception as e:
        print(f"\n✗ 验证失败: {e}")
        return False


def main():
    if not fix_playwright():
        print("\n修复失败，请尝试以下手动步骤：")
        print("1. pip uninstall playwright")
        print("2. pip install playwright")
        print("3. playwright install")
        print("4. playwright install chromium")
        sys.exit(1)
    
    print("\n修复完成！现在可以运行 Playwright 脚本了。")
    print("建议运行: python playwright_chrome_extension.py")


if __name__ == "__main__":
    main()
