{"manifest_version": 3, "name": "CSFloat Market Checker", "short_name": "CSFloat", "version": "5.7.0", "description": "Shows the float value, paint seed, and more of Counter-Strike (CS:GO & CS2) items on the Steam Market or Inventories", "icons": {"16": "icons/16.png", "48": "icons/48.png", "128": "icons/128.png"}, "action": {"default_popup": "src/popup.html", "default_title": "CSFloat Market Checker", "default_icon": {"16": "icons/16.png", "48": "icons/48.png", "128": "icons/128.png"}}, "content_scripts": [{"matches": ["*://*.steamcommunity.com/market/listings/730/*"], "js": ["src/lib/page_scripts/market_listing.js"], "css": ["src/global.css"]}, {"matches": ["*://*.steamcommunity.com/id/*/tradehistory*", "*://*.steamcommunity.com/profiles/*/tradehistory*"], "js": ["src/lib/page_scripts/trade_history.js"], "css": ["src/global.css"]}, {"matches": ["*://*.steamcommunity.com/id/*/inventory*", "*://*.steamcommunity.com/profiles/*/inventory*"], "js": ["src/lib/page_scripts/inventory.js"], "css": ["src/global.css"]}, {"matches": ["*://*.steamcommunity.com/tradeoffer/*"], "js": ["src/lib/page_scripts/trade_offer.js"], "css": ["src/global.css"]}, {"matches": ["*://*.steamcommunity.com/*/tradeoffers/*"], "js": ["src/lib/page_scripts/trade_offers.js"], "css": ["src/global.css"]}, {"matches": ["*://*.steamcommunity.com/id/*", "*://*.steamcommunity.com/profiles/*"], "js": ["src/lib/page_scripts/profile.js"], "css": ["src/global.css"]}, {"matches": ["<all_urls>"], "js": ["src/lib/content_scripts/global_overlay_injector.js"], "run_at": "document_end", "all_frames": false}], "background": {"service_worker": "src/background.js", "type": "module"}, "permissions": ["storage", "scripting", "alarms", "declarativeNetRequestWithHostAccess", "notifications", "tabs"], "optional_permissions": ["activeTab"], "host_permissions": ["*://*.steamcommunity.com/market/listings/730/*", "*://*.steamcommunity.com/id/*/inventory*", "*://*.steamcommunity.com/id/*/tradehistory*", "*://*.steamcommunity.com/profiles/*/inventory*"], "optional_host_permissions": ["*://*.steampowered.com/*", "<all_urls>"], "externally_connectable": {"matches": ["*://*.steamcommunity.com/*", "*://*.csfloat.com/*"]}, "web_accessible_resources": [{"resources": ["src/version.txt"], "matches": ["https://csfloat.com/*"]}, {"resources": ["icons/*"], "matches": ["<all_urls>"]}], "declarative_net_request": {"rule_resources": [{"id": "steamcommunity_ruleset", "enabled": true, "path": "src/steamcommunity_ruleset.json"}]}}