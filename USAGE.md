# Playwright Chrome 插件启动器使用指南

## 🚨 重要说明

**Chrome 插件只能在 Playwright 的 `launch_persistent_context` 模式下正常工作！**

这是因为：
1. Chrome 插件需要持久化的浏览器上下文才能正确加载
2. 普通的 `launch()` 方法无法提供插件所需的环境
3. Manifest v3 插件的 service worker 需要特殊的启动方式
4. Google Chrome 和 Microsoft Edge 移除了侧载插件的命令行标志

## 📁 文件说明

### 核心脚本
- **`playwright_chrome_extension.py`** - 基础启动器（推荐新手使用）
- **`clean_browser_launcher.py`** - 干净浏览器启动器（每次全新环境）⭐
- **`playwright_advanced.py`** - 高级启动器（支持更多选项）
- **`test_extension.py`** - 插件功能测试脚本

### 辅助脚本
- **`setup_playwright.py`** - 环境安装脚本
- **`fix_playwright.py`** - 快速修复脚本
- **`run_playwright.bat`** - Windows 批处理启动器

## 🚀 快速开始

### 1. 安装环境
```bash
# 方法1：使用安装脚本
python setup_playwright.py

# 方法2：手动安装
pip install playwright
playwright install
```

### 2. 启动浏览器并加载插件
```bash
# 干净浏览器模式（推荐 - 每次全新环境）⭐
python clean_browser_launcher.py

# 基础模式
python playwright_chrome_extension.py

# 测试插件功能
python test_extension.py

# Windows 用户可以直接双击
run_playwright.bat  # 选择选项2（干净模式）
```

## 🔧 高级使用

### 命令行选项
```bash
# 普通模式
python playwright_advanced.py

# 无头模式
python playwright_advanced.py --headless

# 运行自动化测试
python playwright_advanced.py --test

# 打开指定网址
python playwright_advanced.py --url "https://steamcommunity.com/market/"

# 使用自定义配置文件
python playwright_advanced.py --profile "./chrome_profile"
```

## 🧪 插件测试

### 推荐测试页面
1. **Steam 市场页面**：
   - https://steamcommunity.com/market/listings/730/AK-47%20%7C%20Redline%20%28Field-Tested%29
   - 应该显示 float 值、磨损等级等信息

2. **Steam 库存页面**：
   - https://steamcommunity.com/id/[用户名]/inventory/
   - 应该在物品上显示额外信息

3. **交易历史页面**：
   - https://steamcommunity.com/id/[用户名]/tradehistory/
   - 应该显示交易物品的详细信息

### 验证插件是否工作
1. 检查页面上是否出现 CSFloat 相关的元素
2. 查看浏览器控制台是否有插件日志
3. 检查插件图标是否出现在浏览器工具栏
4. 尝试点击插件图标打开 popup

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 浏览器启动失败
```
错误: Executable doesn't exist
解决: python fix_playwright.py
```

#### 2. 插件未加载
- 检查 `dist` 目录是否存在
- 确认 `manifest.json` 文件完整
- 使用 `test_extension.py` 进行诊断

#### 3. 插件功能不工作
- 确保使用 `launch_persistent_context` 而不是 `launch`
- 检查插件权限设置
- 查看浏览器控制台错误信息

#### 4. Service Worker 未启动
- 等待更长时间（插件可能需要几秒钟初始化）
- 检查 Manifest v3 配置是否正确
- 使用 `chrome://extensions/` 手动检查插件状态

### 重新安装
```bash
# 完全重新安装
pip uninstall playwright
python setup_playwright.py

# 或者快速修复
python fix_playwright.py
```

## 📝 技术细节

### 为什么必须使用 launch_persistent_context？

1. **插件加载机制**：Chrome 插件需要持久化的用户配置文件才能正确加载
2. **Service Worker**：Manifest v3 插件使用 service worker，需要特殊的启动环境
3. **权限系统**：插件的权限和存储需要持久化上下文
4. **内容脚本注入**：只有在持久化上下文中，内容脚本才能正确注入到页面

### 关键代码差异

❌ **错误方式（不工作）**：
```python
browser = playwright.chromium.launch(args=["--load-extension=..."])
context = browser.new_context()
```

✅ **正确方式（工作）**：
```python
context = playwright.chromium.launch_persistent_context(
    user_data_dir,
    channel="chromium",
    args=["--load-extension=..."]
)
```

## 🎯 最佳实践

1. **总是使用 chromium 通道**：`channel="chromium"`
2. **创建临时用户目录**：避免污染系统配置
3. **等待插件初始化**：给插件 2-3 秒的加载时间
4. **监听 service worker 事件**：确认插件正确启动
5. **适当的错误处理**：提供清晰的错误信息和解决方案

## 📞 获取帮助

如果遇到问题：
1. 运行 `python test_extension.py` 进行诊断
2. 检查浏览器控制台的错误信息
3. 确认插件文件完整性
4. 尝试手动在 Chrome 中加载插件进行对比测试
