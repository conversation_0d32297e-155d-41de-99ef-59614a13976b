import {<PERSON>le} from './lib/bridge/server';
import {InternalResponseBundle} from './lib/bridge/types';
import MessageSender = chrome.runtime.MessageSender;
import {alarmListener, registerTradeAlarmIfPossible} from './lib/alarms/setup';
import {pingTradeStatus} from './lib/alarms/csfloat_trade_pings';
import {registerMarketRefreshAlarm} from './lib/alarms/market_refresh';
import {gStore} from './lib/storage/store';
import {StorageKey} from './lib/storage/keys';
import {isFirefox} from './lib/utils/detect';
import type {MonitorTask, MonitorStatus} from './lib/types/monitor_types';

function unifiedHandler(request: any, sender: MessageSender, sendResponse: (response?: any) => void) {
    console.log('CSFloat Debug: Handling request:', request.request_type, request);

    Handle(request, sender)
        .then((response) => {
            console.log('CSFloat Debug: Request successful:', request.request_type, response);
            sendResponse({
                request_type: request.request_type,
                id: request.id,
                response,
            } as InternalResponseBundle);
        })
        .catch((error) => {
            console.error('CSFloat Debug: Request failed:', request.request_type, error);
            sendResponse({
                request_type: request.request_type,
                id: request.id,
                error: error.toString(),
            } as InternalResponseBundle);
        });
}

/**
 * Gets the count of active monitor tasks
 */
async function getActiveTasksCount(): Promise<number> {
    try {
        const tasks = await gStore.getWithStorage<MonitorTask[]>(
            chrome.storage.local,
            StorageKey.MONITOR_TASKS
        );

        if (!tasks) {
            return 0;
        }

        return tasks.filter(task => task.isActive).length;
    } catch (error) {
        console.error('CSFloat Monitor: Error getting active tasks count:', error);
        return 0;
    }
}

/**
 * Handles monitor triggered events from content scripts
 */
async function handleMonitorTriggered(data: {
    task: MonitorTask;
    itemInfo: any;
    listingInfo: any;
    listingId: string;
}) {
    console.log('CSFloat Monitor: Monitor triggered:', data);

    try {
        // Update monitor status
        const currentStatus = await gStore.getWithStorage<MonitorStatus>(
            chrome.storage.local,
            StorageKey.MONITOR_STATUS
        ) || {
            isActive: true,
            lastRefresh: Date.now(),
            totalChecked: 0,
            totalTriggered: 0,
            activeTasksCount: 0,
            recentMatches: [],
            lastGlobalUpdate: Date.now()
        };

        // Update basic status
        currentStatus.totalTriggered += 1;
        currentStatus.currentTask = data.task.name;
        currentStatus.lastRefresh = Date.now();
        currentStatus.lastGlobalUpdate = Date.now();

        // Update global status fields
        currentStatus.activeTasksCount = await getActiveTasksCount();

        // Add to recent matches (keep only last 10)
        const newMatch = {
            taskName: data.task.name,
            itemName: data.itemInfo.market_hash_name || 'Unknown Item',
            price: (data.listingInfo.converted_price / 100),
            float: data.itemInfo.floatvalue,
            timestamp: Date.now()
        };

        if (!currentStatus.recentMatches) {
            currentStatus.recentMatches = [];
        }
        currentStatus.recentMatches.push(newMatch);
        currentStatus.recentMatches = currentStatus.recentMatches.slice(-10);

        await gStore.setWithStorage(
            chrome.storage.local,
            StorageKey.MONITOR_STATUS,
            currentStatus
        );

        // Create notification if available
        if (chrome.notifications) {
            const price = (data.listingInfo.converted_price / 100).toFixed(2);
            const floatValue = data.itemInfo.floatvalue.toFixed(4);

            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/48.png',
                title: 'CSFloat 监控提醒',
                message: `发现符合条件的饰品：${data.task.name}\n价格：$${price} | Float：${floatValue}`
            });
        }
    } catch (error) {
        console.error('CSFloat Monitor: Error handling monitor triggered:', error);
    }
}

/**
 * Aggregates and updates global monitor status
 */
async function updateGlobalStatus(source: string = 'background'): Promise<MonitorStatus | null> {
    try {
        const currentStatus = await gStore.getWithStorage<MonitorStatus>(
            chrome.storage.local,
            StorageKey.MONITOR_STATUS
        ) || {
            isActive: false,
            lastRefresh: Date.now(),
            totalChecked: 0,
            totalTriggered: 0,
            activeTasksCount: 0,
            recentMatches: [],
            lastGlobalUpdate: Date.now()
        };

        // Update aggregated data
        currentStatus.activeTasksCount = await getActiveTasksCount();
        currentStatus.lastGlobalUpdate = Date.now();

        // Determine if monitoring is active based on active tasks
        currentStatus.isActive = (currentStatus.activeTasksCount || 0) > 0;

        console.log('CSFloat Monitor Background: Updated global status:', {
            source: source,
            activeTasksCount: currentStatus.activeTasksCount,
            isActive: currentStatus.isActive,
            currentStatus: currentStatus
        });

        await gStore.setWithStorage(
            chrome.storage.local,
            StorageKey.MONITOR_STATUS,
            currentStatus
        );

        console.log('CSFloat Monitor: Global status updated from', source, currentStatus);
        return currentStatus;
    } catch (error) {
        console.error('CSFloat Monitor: Error updating global status:', error);
        return null;
    }
}

/**
 * Updates monitor status with check count
 */
async function updateMonitorStatus() {
    try {
        const currentStatus = await gStore.getWithStorage<MonitorStatus>(
            chrome.storage.local,
            StorageKey.MONITOR_STATUS
        );

        if (currentStatus) {
            currentStatus.totalChecked += 1;
            currentStatus.lastRefresh = Date.now();
            currentStatus.lastGlobalUpdate = Date.now();

            // Update active tasks count
            currentStatus.activeTasksCount = await getActiveTasksCount();

            await gStore.setWithStorage(
                chrome.storage.local,
                StorageKey.MONITOR_STATUS,
                currentStatus
            );
        }
    } catch (error) {
        console.error('CSFloat Monitor: Error updating monitor status:', error);
    }
}

/**
 * Handle GET_MONITOR_TASKS message from content scripts
 */
async function handleGetMonitorTasks() {
    try {
        const result = await chrome.storage.local.get('monitor_tasks');
        return { tasks: result.monitor_tasks || [] };
    } catch (error) {
        console.error('CSFloat Monitor: Error getting monitor tasks:', error);
        return { tasks: [] };
    }
}

/**
 * Handle CREATE_NOTIFICATION message from content scripts
 */
function handleCreateNotification(request: any) {
    try {
        console.log('CSFloat Monitor: Creating Windows system notification');

        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: request.iconUrl || 'icons/48.png',
                title: request.title || 'CSFloat 监控提醒',
                message: request.message || '发现符合条件的饰品'
            });

            console.log('CSFloat Monitor: Windows notification created successfully');
        } else {
            console.error('CSFloat Monitor: chrome.notifications not available');
        }
    } catch (error) {
        console.error('CSFloat Monitor: Error creating notification:', error);
    }
}

function requestPermissions(permissions: chrome.runtime.ManifestPermissions[], origins: string[], sendResponse: any) {
    chrome.permissions.request({permissions, origins}, (granted) => sendResponse(granted));

    return true;
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.message === 'requestPermissions') {
        return requestPermissions(request.permissions, request.origins, sendResponse);
    }

    // Handle monitor triggered messages
    if (request.type === 'MONITOR_TRIGGERED') {
        handleMonitorTriggered(request);
        return;
    }

    // Handle get monitor tasks messages
    if (request.type === 'GET_MONITOR_TASKS') {
        handleGetMonitorTasks().then(sendResponse);
        // Update global status when tasks are requested
        updateGlobalStatus('get_tasks');
        return true; // Keep the message channel open for async response
    }

    // Handle create notification messages
    if (request.type === 'CREATE_NOTIFICATION') {
        handleCreateNotification(request);
        return;
    }

    // Handle global status update from popup
    if (request.type === 'UPDATE_GLOBAL_STATUS_FROM_POPUP') {
        updateGlobalStatus(request.source || 'popup');
        return;
    }

    // Handle global status recalculation request
    if (request.type === 'RECALCULATE_GLOBAL_STATUS') {
        console.log('CSFloat Monitor Background: Received RECALCULATE_GLOBAL_STATUS from', request.source);
        updateGlobalStatus(request.source || 'bridge');
        return;
    }

    unifiedHandler(request, sender, sendResponse);
    return true;
});

chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
    unifiedHandler(request, sender, sendResponse);
    return true;
});

if (chrome.alarms) {
    // Install at the root level to make sure events wake up the service worker
    chrome.alarms.onAlarm.addListener(alarmListener);
}

async function checkAlarmState() {
    await registerTradeAlarmIfPossible();
    await registerMarketRefreshAlarm();
}

checkAlarmState();

// Ping trade status upon service worker wake-up
// Why do this even though there's an alarm? Well, some people turn on their device briefly to send a trade offer
// then close it quickly, it's hard to rely on Chrome's scheduling in that case
async function checkTradeStatus() {
    const lastPing = await gStore.getWithStorage<number>(chrome.storage.local, StorageKey.LAST_TRADE_PING_ATTEMPT);
    if (!lastPing || lastPing < Date.now() - 3 * 60 * 1000) {
        // Last ping was over 3 minutes ago
        pingTradeStatus();
    }
}
checkTradeStatus();

if (isFirefox()) {
    // Need to manually update the rule to allow the extension to send trade offers
    // Since Firefox IDs are random and we still want to scope it to only this extension
    browser.declarativeNetRequest
        .updateDynamicRules({
            removeRuleIds: [1738196326],
            addRules: [
                {
                    id: 1738196326,
                    priority: 2,
                    action: {
                        type: 'modifyHeaders',
                        requestHeaders: [
                            {
                                header: 'referer',
                                operation: 'set',
                                value: 'https://steamcommunity.com/tradeoffer/new',
                            },
                        ],
                    },
                    condition: {
                        urlFilter: 'https://steamcommunity.com/tradeoffer/new/send',
                        resourceTypes: ['xmlhttprequest'],
                        initiatorDomains: [new URL(browser.runtime.getURL('')).hostname],
                    },
                },
            ],
        })
        .then(() => {
            console.log('[INFO] Successfully updated ruleset');
        })
        .catch((e) => {
            console.error('[ERROR] Failed to update ruleset for Firefox');
        });
}
