#!/usr/bin/env python3
"""
Playwright 环境设置脚本
自动安装依赖和配置环境

使用方法:
python setup_playwright.py
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        # 使用国内镜像安装 pip 包
        if "pip install" in command and "playwright" in command:
            command += " -i https://pypi.tuna.tsinghua.edu.cn/simple"
        
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print(f"✓ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"✗ {description} 失败")
            print(f"错误: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"✗ {description} 异常: {e}")
        return False
    
    return True


def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 版本过低，需要 Python 3.8 或更高版本")
        return False
    
    print("✓ Python 版本符合要求")
    return True


def install_playwright():
    """安装 Playwright"""
    print("\n开始安装 Playwright...")

    # 先卸载可能存在的旧版本
    run_command(
        "pip uninstall playwright -y",
        "卸载旧版本 Playwright"
    )

    # 安装 playwright 包
    if not run_command(
        "pip install playwright",
        "安装 Playwright Python 包"
    ):
        return False

    # 安装所有浏览器（确保完整安装）
    if not run_command(
        "playwright install",
        "安装所有浏览器"
    ):
        return False

    # 单独安装 Chromium（确保成功）
    if not run_command(
        "playwright install chromium",
        "确保 Chromium 浏览器安装"
    ):
        return False

    # 安装系统依赖（Linux）
    if sys.platform.startswith('linux'):
        run_command(
            "playwright install-deps",
            "安装系统依赖（Linux）"
        )

    return True


def verify_installation():
    """验证安装"""
    print("\n验证安装...")
    
    try:
        import playwright
        print("✓ Playwright 包导入成功")
        
        # 检查浏览器是否安装
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
            print("✓ Chromium 浏览器可以正常启动")
        
        return True
        
    except ImportError:
        print("✗ Playwright 包导入失败")
        return False
    except Exception as e:
        print(f"✗ 浏览器启动失败: {e}")
        return False


def create_requirements_file():
    """创建 requirements.txt 文件"""
    requirements_content = """# Playwright 相关依赖
playwright>=1.40.0

# 可选：异步支持
asyncio

# 可选：图像处理
Pillow>=9.0.0

# 可选：数据处理
pandas>=1.3.0
"""
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements_content)
    
    print("✓ 已创建 requirements.txt 文件")


def create_usage_examples():
    """创建使用示例文件"""
    examples_content = """# Playwright Chrome 插件启动器使用示例

## 基础使用

### 1. 启动浏览器并加载插件
```bash
python playwright_chrome_extension.py
```

### 2. 使用高级启动器
```bash
# 普通模式
python playwright_advanced.py

# 无头模式
python playwright_advanced.py --headless

# 运行自动化测试
python playwright_advanced.py --test

# 打开指定网址
python playwright_advanced.py --url "https://steamcommunity.com/market/"

# 使用自定义配置文件
python playwright_advanced.py --profile "./chrome_profile"
```

## 功能说明

### 基础启动器 (playwright_chrome_extension.py)
- 简单易用，适合快速测试
- 自动加载 CSFloat Market Checker 插件
- 打开测试页面验证插件功能

### 高级启动器 (playwright_advanced.py)
- 支持命令行参数
- 自动化测试功能
- 截图保存
- 自定义用户配置文件

## 插件测试页面

推荐测试页面：
1. Steam 市场页面：https://steamcommunity.com/market/listings/730/
2. Steam 库存页面：https://steamcommunity.com/id/[用户名]/inventory/
3. 交易历史页面：https://steamcommunity.com/id/[用户名]/tradehistory/

## 故障排除

### 常见问题
1. 插件未加载：检查 dist 目录是否存在且包含 manifest.json
2. 页面加载失败：检查网络连接和防火墙设置
3. 浏览器启动失败：确保 Playwright 和 Chromium 正确安装

### 重新安装
```bash
pip uninstall playwright
python setup_playwright.py
```
"""
    
    with open("USAGE.md", "w", encoding="utf-8") as f:
        f.write(examples_content)
    
    print("✓ 已创建 USAGE.md 使用说明文件")


def main():
    print("Playwright 环境设置脚本")
    print("=" * 50)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查插件目录
    extension_path = Path("dist")
    if not extension_path.exists():
        print(f"⚠ 警告: 插件目录 {extension_path} 不存在")
        print("请确保在项目根目录运行此脚本")
    else:
        print(f"✓ 插件目录存在: {extension_path}")
    
    # 安装 Playwright
    if not install_playwright():
        print("\n安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 验证安装
    if not verify_installation():
        print("\n验证失败，请重新运行安装")
        sys.exit(1)
    
    # 创建辅助文件
    create_requirements_file()
    create_usage_examples()
    
    print("\n" + "=" * 50)
    print("✓ Playwright 环境设置完成！")
    print("\n下一步:")
    print("1. 运行基础启动器: python playwright_chrome_extension.py")
    print("2. 运行高级启动器: python playwright_advanced.py")
    print("3. 查看使用说明: cat USAGE.md")


if __name__ == "__main__":
    main()
