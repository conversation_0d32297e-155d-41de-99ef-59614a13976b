import {html, css, TemplateResult} from 'lit';
import {state} from 'lit/decorators.js';
import {FloatElement} from '../custom';
import {CustomElement} from '../injectors';
import {MonitorStatus} from '../../types/monitor_types';

@CustomElement()
export class GlobalOverlay extends FloatElement {

    constructor() {
        super();
        console.log('CSFloat Monitor: GlobalOverlay constructor called');
        console.log('CSFloat Monitor: GlobalOverlay tag:', GlobalOverlay.tag());
    }
    @state() private monitorStatus: MonitorStatus | null = null;
    @state() private isExpanded: boolean = false;
    @state() private position: {x: number, y: number} = {x: 20, y: 20};
    @state() private isDragging: boolean = false;
    @state() private dragOffset: {x: number, y: number} = {x: 0, y: 0};
    @state() private opacity: number = 0.95;

    // Bound methods for event listeners
    private boundHandleMouseMove = this.handleMouseMove.bind(this);
    private boundHandleMouseUp = this.handleMouseUp.bind(this);

    static styles = [
        ...FloatElement.styles,
        css`
            .overlay-container {
                position: fixed;
                z-index: 99999;
                background: rgba(21, 23, 28, 0.95);
                border-radius: 8px;
                padding: 12px;
                min-width: 200px;
                max-width: 350px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                border: 3px solid #ff0000; /* 临时红色边框用于调试 */
                font-family: 'Roboto', Arial, sans-serif;
                font-size: 12px;
                color: #ebebeb;
                user-select: none;
                transition: all 0.2s ease;
                top: 20px;
                left: 20px;
            }

            .overlay-container.dragging {
                transition: none;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
            }

            .overlay-container.dragging * {
                pointer-events: none;
                user-select: none;
            }

            .overlay-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                cursor: move;
                padding: 4px 0;
            }

            .overlay-title {
                font-weight: 500;
                color: #ffffff;
                font-size: 13px;
            }

            .expand-button {
                background: none;
                border: none;
                color: #ebebeb;
                cursor: pointer;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 12px;
                transition: background-color 0.2s ease;
            }

            .expand-button:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }

            .status-compact {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 11px;
            }

            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #4CAF50;
            }

            .status-indicator.inactive {
                background-color: #666;
            }

            .status-text {
                color: #ebebeb;
            }

            .status-expanded {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .status-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
                font-size: 11px;
            }

            .status-label {
                color: #999;
            }

            .status-value {
                color: #ebebeb;
                font-weight: 500;
            }

            .recent-matches {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .recent-matches-title {
                font-size: 11px;
                color: #999;
                margin-bottom: 6px;
            }

            .match-item {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
                padding: 6px;
                margin-bottom: 4px;
                font-size: 10px;
            }

            .match-item:last-child {
                margin-bottom: 0;
            }

            .match-name {
                color: #4CAF50;
                font-weight: 500;
                margin-bottom: 2px;
            }

            .match-details {
                color: #999;
                display: flex;
                justify-content: space-between;
            }

            .hidden {
                display: none;
            }
        `,
    ];

    connectedCallback() {
        super.connectedCallback();
        this.loadStoredPosition();
        this.loadStoredExpandState();
        this.startStorageListener();
        this.loadInitialStatus();
    }

    disconnectedCallback() {
        super.disconnectedCallback();
        this.stopStorageListener();
    }

    private async loadStoredPosition() {
        try {
            const result = await chrome.storage.local.get(['globalOverlayPosition']);
            if (result.globalOverlayPosition) {
                this.position = result.globalOverlayPosition;
                this.updatePosition();
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error loading stored position:', error);
        }
    }

    private async loadStoredExpandState() {
        try {
            const result = await chrome.storage.local.get(['globalOverlayExpanded']);
            if (result.globalOverlayExpanded !== undefined) {
                this.isExpanded = result.globalOverlayExpanded;
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error loading expand state:', error);
        }
    }

    private async savePosition() {
        try {
            await chrome.storage.local.set({
                globalOverlayPosition: this.position
            });
        } catch (error) {
            console.error('CSFloat Monitor: Error saving position:', error);
        }
    }

    private async saveExpandState() {
        try {
            await chrome.storage.local.set({
                globalOverlayExpanded: this.isExpanded
            });
        } catch (error) {
            console.error('CSFloat Monitor: Error saving expand state:', error);
        }
    }

    private startStorageListener() {
        chrome.storage.onChanged.addListener(this.handleStorageChange.bind(this));
    }

    private stopStorageListener() {
        chrome.storage.onChanged.removeListener(this.handleStorageChange.bind(this));
    }

    private handleStorageChange(changes: any) {
        if (changes['monitor_status']) {
            const newStatus = changes['monitor_status'].newValue;
            if (newStatus) {
                try {
                    this.monitorStatus = typeof newStatus === 'string' ? JSON.parse(newStatus) : newStatus;
                } catch (error) {
                    console.error('CSFloat Monitor: Error parsing monitor status:', error);
                }
            }
        }

        // Handle opacity changes
        if (changes['overlayOpacity']) {
            const newOpacity = changes['overlayOpacity'].newValue;
            if (typeof newOpacity === 'number') {
                this.opacity = newOpacity;
            }
        }
    }

    private async loadInitialStatus() {
        try {
            const result = await chrome.storage.local.get(['monitor_status', 'overlayOpacity']);

            if (result.monitor_status) {
                const status = typeof result.monitor_status === 'string'
                    ? JSON.parse(result.monitor_status)
                    : result.monitor_status;
                this.monitorStatus = status;
            }

            // Load opacity setting
            if (typeof result.overlayOpacity === 'number') {
                this.opacity = result.overlayOpacity;
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error loading initial status:', error);
        }
    }

    private updatePosition() {
        this.style.left = `${this.position.x}px`;
        this.style.top = `${this.position.y}px`;
    }

    private handleMouseDown(e: MouseEvent) {
        if (e.target === this.shadowRoot?.querySelector('.overlay-header') || 
            (e.target as Element)?.closest('.overlay-header')) {
            this.isDragging = true;
            this.dragOffset = {
                x: e.clientX - this.position.x,
                y: e.clientY - this.position.y
            };
            
            document.addEventListener('mousemove', this.boundHandleMouseMove);
            document.addEventListener('mouseup', this.boundHandleMouseUp);
            e.preventDefault();
        }
    }

    private handleMouseMove(e: MouseEvent) {
        if (this.isDragging) {
            // Get overlay dimensions for better boundary checking
            const overlayElement = this.shadowRoot?.querySelector('.overlay-container') as HTMLElement;
            const overlayWidth = overlayElement?.offsetWidth || 200;
            const overlayHeight = overlayElement?.offsetHeight || 100;

            // Calculate new position with proper boundary checks
            const newX = Math.max(0, Math.min(window.innerWidth - overlayWidth, e.clientX - this.dragOffset.x));
            const newY = Math.max(0, Math.min(window.innerHeight - overlayHeight, e.clientY - this.dragOffset.y));

            this.position = { x: newX, y: newY };
            this.updatePosition();
        }
    }

    private handleMouseUp() {
        if (this.isDragging) {
            this.isDragging = false;
            this.savePosition();
            document.removeEventListener('mousemove', this.boundHandleMouseMove);
            document.removeEventListener('mouseup', this.boundHandleMouseUp);
        }
    }

    private toggleExpanded() {
        this.isExpanded = !this.isExpanded;
        this.saveExpandState();
    }

    public resetPosition() {
        this.position = { x: 20, y: 20 };
        this.updatePosition();
        this.savePosition();
    }

    private formatTime(timestamp: number): string {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}小时前`;
        
        const days = Math.floor(hours / 24);
        return `${days}天前`;
    }

    render(): TemplateResult {
        if (!this.monitorStatus || !this.monitorStatus.isActive || (this.monitorStatus.activeTasksCount || 0) === 0) {
            return html``;
        }

        const isActive = this.monitorStatus.isActive && (this.monitorStatus.activeTasksCount || 0) > 0;

        return html`
            <div class="overlay-container ${this.isDragging ? 'dragging' : ''}"
                 style="opacity: ${this.opacity}"
                 @mousedown="${this.handleMouseDown}">
                <div class="overlay-header">
                    <div class="overlay-title">CSFloat 监控</div>
                    <button class="expand-button" @click="${this.toggleExpanded}">
                        ${this.isExpanded ? '−' : '+'}
                    </button>
                </div>
                
                <div class="status-compact">
                    <div class="status-indicator ${isActive ? '' : 'inactive'}"></div>
                    <div class="status-text">
                        ${isActive ? `${this.monitorStatus.activeTasksCount} 个任务活跃` : '监控已暂停'}
                    </div>
                </div>

                <div class="status-expanded ${this.isExpanded ? '' : 'hidden'}">
                    <div class="status-row">
                        <span class="status-label">总检查:</span>
                        <span class="status-value">${this.monitorStatus.totalChecked || 0}</span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">总触发:</span>
                        <span class="status-value">${this.monitorStatus.totalTriggered || 0}</span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">最后更新:</span>
                        <span class="status-value">${this.formatTime(this.monitorStatus.lastRefresh)}</span>
                    </div>
                    
                    ${this.renderRecentMatches()}
                </div>
            </div>
        `;
    }

    private renderRecentMatches(): TemplateResult {
        const matches = this.monitorStatus?.recentMatches || [];
        if (matches.length === 0) {
            return html``;
        }

        return html`
            <div class="recent-matches">
                <div class="recent-matches-title">最近匹配 (${matches.length})</div>
                ${matches.slice(-3).reverse().map(match => html`
                    <div class="match-item">
                        <div class="match-name">${match.taskName}</div>
                        <div class="match-details">
                            <span>$${match.price.toFixed(2)}</span>
                            <span>Float: ${match.float.toFixed(4)}</span>
                        </div>
                    </div>
                `)}
            </div>
        `;
    }
}
