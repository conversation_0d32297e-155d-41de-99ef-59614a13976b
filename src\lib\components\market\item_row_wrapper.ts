import {css, html, nothing, TemplateR<PERSON>ult} from 'lit';

import {state} from 'lit/decorators.js';
import {CustomElement, InjectAppend, InjectionMode} from '../injectors';
import {FloatElement} from '../custom';
import {cache} from 'decorator-cache-getter';
import {rgAsset, ListingData} from '../../types/steam';
import {gFloatFetcher} from '../../services/float_fetcher';
import {ItemInfo} from '../../bridge/handlers/fetch_inspect_info';
import {getMarketInspectLink, inlineEasyInspect} from './helpers';
import {
    formatSeed,
    getFadePercentage,
    isSkin,
    renderClickableRank,
    floor,
    isCharm,
    isBlueSkin,
    isHighlightCharm,
} from '../../utils/skin';
import {gFilterService} from '../../services/filter';
import {gMonitorService} from '../../services/monitor_service';
import {AppId, ContextId, Currency} from '../../types/steam_constants';
import {defined} from '../../utils/checkers';
import {pickTextColour} from '../../utils/colours';
import '../common/ui/floatbar';
import './sticker_display';
import {FetchBluegem, FetchBluegemResponse} from '../../bridge/handlers/fetch_bluegem';
import {ClientSend} from '../../bridge/client';
import {CreateNotification} from '../../bridge/handlers/create_notification';
import {UpdateGlobalStatus} from '../../bridge/handlers/update_global_status';
import {Get} from '../../bridge/handlers/storage_get';
import {MONITOR_STATUS} from '../../storage/keys';
import type {MonitorTask, MonitorStatus} from '../../types/monitor_types';
import {ConflictingExtension, ConflictingMode, HideConflictingElement, StyleConflictingElement} from '../decorators';

@CustomElement()
@InjectAppend('#searchResultsRows .market_listing_row .market_listing_item_name_block', InjectionMode.CONTINUOUS)
@HideConflictingElement(
    ConflictingExtension.CS2_TRADER,
    '#searchResultsRows .market_listing_row .stickerHolderMarket, #searchResultsRows .market_listing_row .stickersTotal, #searchResultsRows .market_listing_row .floatBarMarket',
    ConflictingMode.CONTINUOUS
)
@HideConflictingElement(
    ConflictingExtension.SIH,
    '#searchResultsRows .market_listing_row .sih-images, #searchResultsRows .market_listing_row .sih-keychains'
)
@StyleConflictingElement(
    ConflictingExtension.SIH,
    '#searchResultsRows .market_listing_row .market_listing_item_name_block',
    ConflictingMode.ONCE,
    {'max-width': '100%', 'margin-top': '8px'}
)
export class ItemRowWrapper extends FloatElement {
    static styles = [
        ...FloatElement.styles,
        css`
            .float-row-wrapper {
                display: inline-block;
                margin-bottom: 5px;
            }
        `,
    ];

    @cache
    get listingId(): string | undefined {
        const id = $J(this).parent().find('.market_listing_item_name').attr('id');
        const matches = id?.match(/listing_(\d+)_name/);
        if (!matches || matches.length < 2) {
            return;
        }

        return matches[1];
    }

    get listingInfo(): ListingData | null {
        return g_rgListingInfo[this.listingId!];
    }

    get asset(): rgAsset | undefined {
        if (!this.listingInfo) return;

        return g_rgAssets[AppId.CSGO][ContextId.PRIMARY][this.listingInfo.asset.id!];
    }

    get inspectLink(): string | undefined {
        return getMarketInspectLink(this.listingId!);
    }

    async fetchFloat(): Promise<ItemInfo> {
        return gFloatFetcher.fetch({
            link: this.inspectLink!,
            listPrice: this.usdPrice,
        });
    }

    /**
     * Returns the price of the item in the user's wallet currency
     *
     * If the user is not logged in, this will return undefined
     */
    get convertedPrice(): number | undefined {
        if (!defined(typeof g_rgWalletInfo) || !g_rgWalletInfo || !g_rgWalletInfo.wallet_currency) {
            return;
        }

        if (!this.listingInfo || !this.listingInfo.converted_price || !this.listingInfo.converted_fee) {
            return;
        }

        // Item currency is formatted as 20XX for most currencies where XX is the account currency
        if (this.listingInfo.converted_currencyid !== g_rgWalletInfo.wallet_currency + 2000) {
            return;
        }

        return (this.listingInfo.converted_price + this.listingInfo.converted_fee) / 100;
    }

    get usdPrice(): number | undefined {
        if (this.listingInfo?.currencyid === Currency.USD) {
            return this.listingInfo.price + this.listingInfo.fee;
        } else if (this.listingInfo?.converted_currencyid === Currency.USD) {
            return this.listingInfo.converted_price! + this.listingInfo.converted_fee!;
        }
    }

    @state()
    private itemInfo: ItemInfo | undefined;
    @state()
    private error: string | undefined;

    @state()
    private bluegemData: FetchBluegemResponse | undefined;

    async connectedCallback() {
        super.connectedCallback();

        if (!this.inspectLink) {
            return;
        }

        // Only add if they don't have Steam Inventory Helper
        if (!$J(this).parent().parent().find('.sih-inspect-magnifier').length) {
            inlineEasyInspect($J(this).parent().parent().find('.market_listing_item_img_container'), this.inspectLink);
        }

        // If the item is a highlight charm, we're done here
        if (this.asset && isHighlightCharm(this.asset)) {
            return;
        }

        try {
            this.itemInfo = await this.fetchFloat();

            // New: Monitor check logic
            await this.checkMonitorConditions();

            // New: Add monitor status indicator
            this.addMonitorStatusIndicator();
        } catch (e: any) {
            this.error = e.toString();
        }

        if (this.itemInfo && this.asset) {
            // Create a sticker display element
            const stickerDisplay = document.createElement('csfloat-sticker-display');
            stickerDisplay.classList.add('economy_item_hoverable');
            const elementId = `listing_${this.listingId}_csfloat`;
            stickerDisplay.id = elementId;
            // @ts-ignore - We know these properties exist on our Lit element
            stickerDisplay.itemInfo = this.itemInfo;
            // @ts-ignore
            stickerDisplay.asset = this.asset;

            // Remove Steam's inspect button
            const itemNameBlock = $J(this).parent().parent().find('.market_listing_item_name_block');
            itemNameBlock.parent().find('.market_listing_row_action')?.parent().remove();
            // Remove Steam's stickers and keychains
            itemNameBlock.parent().find('.market_listing_row_details')?.remove();

            // Only add if not already present
            if (!itemNameBlock.find('csfloat-sticker-display').length) {
                itemNameBlock.prepend(stickerDisplay);
            }

            CreateItemHoverFromContainer(
                g_rgAssets,
                elementId,
                this.asset.appid,
                this.asset.contextid,
                this.asset.id,
                this.asset.amount
            );
        }

        if (this.itemInfo) {
            gFilterService.onUpdate$.subscribe(() => {
                const colour = gFilterService.matchColour(this.itemInfo!, this.convertedPrice) || '';
                $J(this).parent().parent().css('background-color', colour);
                const textColour = colour ? pickTextColour(colour, '#8F98A0', '#484848') : '';
                $J(this).css('color', textColour);
            });
        }

        // Fetch bluegem data if needed
        if (this.itemInfo && this.asset && isBlueSkin(this.itemInfo)) {
            try {
                this.bluegemData = await ClientSend(FetchBluegem, {
                    iteminfo: this.itemInfo,
                });
            } catch (e: any) {
                console.error(`Failed to fetch bluegem for ${this.asset.id}: ${e.toString()}`);
                this.bluegemData = undefined;
            }
        } else {
            this.bluegemData = undefined;
        }

        if (
            MarketCheckHash &&
            defined(typeof BuyItemDialog) &&
            (!BuyItemDialog?.m_modal || !BuyItemDialog.m_modal.m_bVisible)
        ) {
            // Only check the hash if the item dialog has not been initialized OR
            // it is no longer visible. Prevents "freezing" the page with multiple
            // dialogs opening.
            MarketCheckHash();
        }

        // Make sure the parent containers can overflow for tooltips
        const parentContainer = $J(this).parent();
        if (parentContainer) {
            parentContainer.css('overflow', 'visible');
            parentContainer.parent().css('overflow', 'visible');
        }
    }

    render() {
        if (!this.inspectLink) {
            return html``;
        }

        if (!this.asset) {
            return nothing;
        }

        if (this.asset && !isSkin(this.asset) && !isCharm(this.asset)) {
            return nothing;
        }

        if (this.itemInfo && isSkin(this.asset)) {
            const fadePercentage = this.asset && getFadePercentage(this.asset, this.itemInfo)?.percentage;

            return html`
                <div class="float-row-wrapper">
                    ${this.renderFloatBar()}
                    <span style="display: block;">
                        Float: ${this.itemInfo.floatvalue.toFixed(14)} ${renderClickableRank(this.itemInfo)}
                    </span>
                    Paint Seed:
                    ${formatSeed(this.itemInfo)}${fadePercentage !== undefined
                        ? html`<br />
                              Fade: ${floor(fadePercentage, 5)}%`
                        : nothing}
                    ${this.renderBluegem()}
                </div>
            `;
        } else if (this.itemInfo && isCharm(this.asset)) {
            return html`
                <div class="float-row-wrapper">
                    Pattern: #${this.itemInfo.keychains?.length > 0 ? this.itemInfo.keychains[0].pattern : 'Unknown'}
                </div>
            `;
        } else if (this.error) {
            return html`<div style="color: orangered">CSFloat ${this.error}</div>`;
        } else {
            return html`<div>Loading...</div>`;
        }
    }

    renderBluegem(): TemplateResult<1> {
        if (!this.itemInfo || !this.bluegemData) {
            return html``;
        }

        // Some skins got only one blue value
        if (this.bluegemData.backside_blue === undefined) {
            return html`<div>Blue: ${this.bluegemData.playside_blue}%</div>`;
        }

        return html`<div>
            Blue (${this.bluegemData.placement}): ${this.bluegemData.playside_blue}% /
            ${this.bluegemData.backside_blue}%
        </div>`;
    }

    /**
     * Checks if any active monitor task matches the current item conditions
     * Triggers monitor events and updates task state if conditions are met
     */
    private async checkMonitorConditions() {
        if (!this.itemInfo || !this.listingInfo || !this.asset) return;

        try {
            const matchedTask = gMonitorService.checkConditions(
                this.asset.market_hash_name,
                this.listingInfo.converted_price || 0,
                this.itemInfo.floatvalue
            );

            if (matchedTask) {
                console.log('CSFloat Monitor: Found matching item!', {
                    task: matchedTask.name,
                    item: this.asset.market_hash_name,
                    price: this.listingInfo.converted_price,
                    float: this.itemInfo.floatvalue
                });

                // Send notification using CSFloat's bridge system
                this.sendMonitorNotification(matchedTask);

                // Update global status with new match
                await this.updateGlobalStatusForMatch(matchedTask);

                // Update task: decrease quantity and set last triggered time
                await gMonitorService.updateTask(matchedTask.id, {
                    lastTriggered: Date.now(),
                    quantity: Math.max(0, matchedTask.quantity - 1)
                });

                // Disable task if quantity reaches 0
                if (matchedTask.quantity <= 1) {
                    await gMonitorService.updateTask(matchedTask.id, { isActive: false });
                    console.log('CSFloat Monitor: Task completed and disabled:', matchedTask.name);
                }
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error in checkMonitorConditions:', error);
        }
    }

    /**
     * Update global status when a match is found
     */
    private async updateGlobalStatusForMatch(task: MonitorTask) {
        try {
            const price = ((this.listingInfo?.converted_price || 0) / 100);
            const floatValue = this.itemInfo?.floatvalue || 0;

            // Create new match record
            const newMatch = {
                taskName: task.name,
                itemName: this.asset?.market_hash_name || 'Unknown Item',
                price: price,
                float: floatValue,
                timestamp: Date.now()
            };

            // Get current status to update recentMatches
            const currentMatches = await this.getCurrentRecentMatches();
            const updatedMatches = [...currentMatches, newMatch].slice(-10); // Keep last 10

            // Send global status update
            await ClientSend(UpdateGlobalStatus, {
                statusUpdate: {
                    totalTriggered: undefined, // Will be incremented by background script
                    recentMatches: updatedMatches,
                    currentPage: window.location.href,
                    lastGlobalUpdate: Date.now()
                },
                source: 'market_listing'
            });

            console.log('CSFloat Monitor: Global status updated for match:', task.name);
        } catch (error) {
            console.error('CSFloat Monitor: Error updating global status for match:', error);
        }
    }

    private async getCurrentRecentMatches(): Promise<Array<{taskName: string, itemName: string, price: number, float: number, timestamp: number}>> {
        try {
            // Use CSFloat's bridge system to get storage data
            const status = await Get(MONITOR_STATUS);
            if (status && status.recentMatches) {
                return status.recentMatches;
            }
            return [];
        } catch (error) {
            console.error('CSFloat Monitor: Error getting current recent matches:', error);
            return [];
        }
    }

    /**
     * Send monitor notification using CSFloat's bridge system
     */
    private async sendMonitorNotification(task: MonitorTask) {
        try {
            const price = ((this.listingInfo?.converted_price || 0) / 100).toFixed(2);
            const floatValue = (this.itemInfo?.floatvalue || 0).toFixed(4);

            // Send notification request through CSFloat's bridge system
            await ClientSend(CreateNotification, {
                title: 'CSFloat 监控提醒',
                message: `发现符合条件的饰品：${task.name}\n价格：$${price} | Float：${floatValue}`,
                iconUrl: 'icons/48.png'
            });

            console.log('CSFloat Monitor: Notification request sent via bridge for task:', task.name);
        } catch (error) {
            console.error('CSFloat Monitor: Error sending notification via bridge:', error);
        }
    }

    /**
     * Adds a visual indicator to show if this item is being monitored
     */
    private addMonitorStatusIndicator() {
        if (!this.asset) {
            console.log('CSFloat Monitor: No asset available for status indicator');
            return;
        }

        try {
            console.log('CSFloat Monitor: Checking monitor status for:', this.asset.market_hash_name);
            const activeTasks = gMonitorService.getTasksByMarketHashName(this.asset.market_hash_name)
                .filter(task => task.isActive);

            console.log('CSFloat Monitor: Found active tasks:', activeTasks.length);

            if (activeTasks.length > 0) {
                // Find the item name container
                const itemNameBlock = $J(this).parent().parent().find('.market_listing_item_name_block');

                // Remove existing monitor indicator
                itemNameBlock.find('.csfloat-monitor-indicator').remove();

                // Create monitor indicator
                const indicator = $J(`
                    <div class="csfloat-monitor-indicator" style="
                        display: inline-block;
                        background: #4CAF50;
                        color: white;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 11px;
                        font-weight: bold;
                        margin-left: 8px;
                        vertical-align: middle;
                    ">
                        🎯 监控中 (${activeTasks.length})
                    </div>
                `);

                // Add indicator after the item name
                itemNameBlock.find('.market_listing_item_name').append(indicator);
            }
        } catch (error) {
            console.error('CSFloat Monitor: Error adding status indicator:', error);
        }
    }

    renderFloatBar(): TemplateResult<1> {
        if (!this.itemInfo) {
            return html``;
        }

        return html`
            <csfloat-float-bar
                float=${this.itemInfo.floatvalue}
                minFloat=${this.itemInfo.min}
                maxFloat=${this.itemInfo.max}
            >
            </csfloat-float-bar>
        `;
    }
}
