export enum RequestType {
    EXECUTE_SCRIPT_ON_PAGE = 0,
    EXECUTE_CSS_ON_PAGE = 1,
    FETCH_INSPECT_INFO = 2,
    FETCH_STALL = 3,
    STORAGE_GET = 4,
    STORAGE_SET = 5,
    STORAGE_REMOVE = 6,
    FETCH_PENDING_TRADES = 7,
    FETCH_EXTENSION_FILE = 8,
    ANNOTATE_OFFER = 9,
    EXTENSION_VERSION = 10,
    TRADE_HISTORY_STATUS = 11,
    TRADE_OFFER_STATUS = 12,
    HAS_PERMISSIONS = 13,
    PING_SETUP_EXTENSION = 14,
    PING_EXTENSION_STATUS = 15,
    PING_CANCEL_TRADE = 16,
    CREATE_TRADE_OFFER = 17,
    FETCH_STEAM_USER = 18,
    PING_TRADE_STATUS = 19,
    PING_STATUS = 20,
    FETCH_OWN_INVENTORY = 21,
    <PERSON><PERSON><PERSON>_TRADE_OFFER = 22,
    FET<PERSON>_STEAM_TRADES = 23,
    FETCH_BLOCKED_USERS = 24,
    PING_BLOCKED_USERS = 25,
    FETCH_BLUEGEM = 26,
    LIST_ITEM = 27,
    FETCH_RECOMMENDED_PRICE = 28,
    FETCH_CSFLOAT_ME = 29,
    PING_ROLLBACK_TRADE = 30,
    FETCH_TRADE_HISTORY = 31,
    CREATE_NOTIFICATION = 32,
    UPDATE_GLOBAL_STATUS = 33,
}
