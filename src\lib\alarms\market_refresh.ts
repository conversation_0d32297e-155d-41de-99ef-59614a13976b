import {HasPermissions} from '../bridge/handlers/has_permissions';
import {gStore} from '../storage/store';
import {StorageKey} from '../storage/keys';

export const MONITOR_MARKET_ALARM_NAME = 'monitor_market_refresh';

/**
 * Refreshes market data by calling Steam's native page refresh mechanism
 * Includes debounce protection to avoid excessive requests
 */
export async function refreshMarketData() {
    // This function is now simplified since we use setInterval in page context
    console.log('CSFloat Monitor: refreshMarketData called from background (no-op)');
}

/**
 * Registers the market refresh alarm with Chrome's alarm system
 * Follows the same pattern as the existing trade status alarm
 */
export async function registerMarketRefreshAlarm() {
    // Check if we have the required permissions
    const hasPermissions = await HasPermissions.handleRequest({permissions: ['alarms'], origins: []}, {});
    if (!hasPermissions.granted) {
        console.log('CSFloat Monitor: Alarms permission not granted, skipping market refresh alarm registration');
        return;
    }

    // Check if alarm already exists
    const alarm = await chrome.alarms.get(MONITOR_MARKET_ALARM_NAME);
    
    // Only create alarm if it doesn't exist
    if (!alarm) {
        await chrome.alarms.create(MONITOR_MARKET_ALARM_NAME, {
            periodInMinutes: 0.5, // 30 seconds interval
            delayInMinutes: 0.1   // Start after 6 seconds
        });
        console.log('CSFloat Monitor: Market refresh alarm registered');
    }
}
