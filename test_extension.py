#!/usr/bin/env python3
"""
Chrome 插件功能测试脚本
专门测试 CSFloat Market Checker 插件是否正常工作

使用方法:
python test_extension.py
"""

import asyncio
import json
import tempfile
import time
from pathlib import Path
from playwright.async_api import async_playwright


async def test_extension_functionality():
    """测试插件功能"""
    # 获取插件路径
    script_dir = Path(__file__).parent.absolute()
    extension_path = script_dir / "dist"
    
    if not extension_path.exists():
        print("❌ 插件目录不存在")
        return False
    
    print(f"🔧 插件路径: {extension_path}")
    
    # 创建全新的临时用户数据目录（确保每次都是干净环境）
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    user_data_dir = tempfile.mkdtemp(prefix=f"playwright_test_clean_{unique_id}_")
    print(f"📁 全新用户数据目录: {user_data_dir}")
    print("🧹 测试环境完全干净（无 cookies、session 等）")
    
    async with async_playwright() as p:
        try:
            print("🚀 启动浏览器...")
            
            # 使用 launch_persistent_context（插件必需）
            context = await p.chromium.launch_persistent_context(
                user_data_dir,
                headless=False,
                channel="chromium",
                args=[
                    f"--load-extension={extension_path}",
                    f"--disable-extensions-except={extension_path}",
                    "--disable-web-security",
                    "--no-sandbox",
                    "--incognito",  # 无痕模式
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--disable-default-apps",
                    "--no-first-run",
                    "--disable-component-update",
                ]
            )
            
            print("✅ 浏览器启动成功")
            
            # 等待插件加载
            print("⏳ 等待插件加载...")
            await asyncio.sleep(3)
            
            # 检查 service worker
            service_workers = context.service_workers
            if service_workers:
                print(f"✅ 检测到 {len(service_workers)} 个 service worker")
                for i, sw in enumerate(service_workers):
                    print(f"   Service Worker {i+1}: {sw.url}")
            else:
                print("⏳ 等待 service worker 启动...")
                try:
                    service_worker = await context.wait_for_event('serviceworker', timeout=10000)
                    print(f"✅ Service worker 已启动: {service_worker.url}")
                except:
                    print("⚠️ 未检测到 service worker")
            
            # 获取插件 ID
            extension_id = None
            if service_workers:
                sw_url = service_workers[0].url
                extension_id = sw_url.split("/")[2]
                print(f"🆔 插件 ID: {extension_id}")
            
            # 创建测试页面
            page = await context.new_page()
            
            # 测试 1: 检查插件 popup 页面
            if extension_id:
                print("\n📋 测试 1: 插件 popup 页面")
                try:
                    popup_url = f"chrome-extension://{extension_id}/src/popup.html"
                    await page.goto(popup_url, timeout=10000)
                    print(f"✅ Popup 页面加载成功: {popup_url}")
                    
                    # 检查页面内容
                    title = await page.title()
                    print(f"   页面标题: {title}")
                    
                    # 截图
                    await page.screenshot(path="popup_test.png")
                    print("   📸 Popup 截图已保存: popup_test.png")
                    
                except Exception as e:
                    print(f"❌ Popup 页面测试失败: {e}")
            
            # 测试 2: Steam 市场页面
            print("\n📋 测试 2: Steam 市场页面插件功能")
            test_url = "https://steamcommunity.com/market/listings/730/AK-47%20%7C%20Redline%20%28Field-Tested%29"
            
            try:
                print(f"🌐 访问: {test_url}")
                await page.goto(test_url, timeout=30000)
                print("✅ 页面加载成功")
                
                # 等待页面完全加载
                await asyncio.sleep(5)
                
                # 检查插件是否注入了内容
                print("🔍 检查插件注入的内容...")
                
                # 查找可能的插件元素
                selectors_to_check = [
                    '[class*="csfloat"]',
                    '[id*="csfloat"]',
                    '[data-csfloat]',
                    '.float-value',
                    '.paint-seed',
                    '.wear-rating'
                ]
                
                found_elements = 0
                for selector in selectors_to_check:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        found_elements += len(elements)
                        print(f"   ✅ 找到 {len(elements)} 个元素: {selector}")
                
                if found_elements > 0:
                    print(f"✅ 插件功能正常，共找到 {found_elements} 个插件元素")
                else:
                    print("⚠️ 未找到明显的插件元素，可能需要更长时间加载")
                
                # 检查控制台日志
                print("📝 检查控制台日志...")
                
                # 监听控制台消息
                console_messages = []
                def handle_console(msg):
                    console_messages.append(f"{msg.type}: {msg.text}")
                
                page.on("console", handle_console)
                
                # 等待一段时间收集日志
                await asyncio.sleep(3)
                
                # 显示相关日志
                csfloat_logs = [msg for msg in console_messages if 'csfloat' in msg.lower()]
                if csfloat_logs:
                    print("   插件相关日志:")
                    for log in csfloat_logs[:5]:  # 只显示前5条
                        print(f"     {log}")
                
                # 截图
                await page.screenshot(path="market_test.png", full_page=True)
                print("   📸 市场页面截图已保存: market_test.png")
                
            except Exception as e:
                print(f"❌ 市场页面测试失败: {e}")
            
            # 测试 3: 检查插件权限和配置
            print("\n📋 测试 3: 插件配置检查")
            try:
                # 读取 manifest.json
                manifest_path = extension_path / "manifest.json"
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    manifest = json.load(f)
                
                print(f"   插件名称: {manifest.get('name')}")
                print(f"   版本: {manifest.get('version')}")
                print(f"   Manifest 版本: {manifest.get('manifest_version')}")
                
                # 检查权限
                permissions = manifest.get('permissions', [])
                print(f"   权限: {', '.join(permissions)}")
                
                # 检查内容脚本
                content_scripts = manifest.get('content_scripts', [])
                print(f"   内容脚本数量: {len(content_scripts)}")
                
                for i, cs in enumerate(content_scripts):
                    matches = cs.get('matches', [])
                    js_files = cs.get('js', [])
                    print(f"     脚本 {i+1}: {len(matches)} 个匹配规则, {len(js_files)} 个 JS 文件")
                
            except Exception as e:
                print(f"❌ 配置检查失败: {e}")
            
            print("\n🎯 测试完成！")
            print("按 Enter 键关闭浏览器...")
            input()
            
            await context.close()
            print("✅ 浏览器已关闭")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        
        finally:
            # 清理临时目录
            import shutil
            try:
                shutil.rmtree(user_data_dir)
                print("🧹 临时文件已清理")
            except:
                print(f"⚠️ 临时目录清理失败: {user_data_dir}")


async def main():
    print("CSFloat Market Checker - 插件功能测试")
    print("=" * 50)
    
    success = await test_extension_functionality()
    
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
        print("请检查:")
        print("1. Playwright 是否正确安装")
        print("2. dist 目录是否存在且包含有效的插件文件")
        print("3. 网络连接是否正常")


if __name__ == "__main__":
    asyncio.run(main())
