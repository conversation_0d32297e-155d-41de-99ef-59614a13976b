<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                width: 600px;
                min-height: 400px;
                padding: 32px;
                background-color: #15171c;
            }
            button {
                width: 100%;
                padding: 20px;
                background-color: rgba(35, 123, 255, 0.15);
                color: #237bff;
                border: none;
                border-radius: 16px;
                cursor: pointer;
                font-family: 'Roboto', serif;
                font-size: 28px;
                font-weight: 500;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 16px;
            }
            button:hover {
                background-color: #2a374d;
            }
            button:disabled {
                cursor: not-allowed;
                background-color: rgba(255, 255, 255, .12);
                color: rgba(255, 255, 255, .5);
            }
            h3 {
                color: white;
                margin-bottom: 20px;
                font-family: 'Roboto', serif;
                font-size: 32px;
                font-weight: 500;
            }
            input {
                width: 100%;
                margin-bottom: 20px;
                padding: 16px;
                border-radius: 8px;
                border: none;
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                font-family: 'Roboto', serif;
                font-size: 24px;
            }
            input::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
            .task-item {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                margin-bottom: 16px;
                border-radius: 8px;
            }
            .task-name {
                color: white;
                font-weight: bold;
                margin-bottom: 8px;
                font-size: 28px;
            }
            .task-details {
                color: #ccc;
                font-size: 24px;
                margin-bottom: 10px;
            }
            .task-buttons {
                display: flex;
                gap: 10px;
            }
            .task-buttons button {
                font-size: 24px;
                padding: 8px 16px;
                width: auto;
                flex: none;
            }
            .btn-active {
                background: #4CAF50 !important;
            }
            .btn-inactive {
                background: #666 !important;
            }
            .btn-delete {
                background: #f44336 !important;
            }
            .modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 1000;
                overflow-y: auto;
            }
            .modal-content {
                background: #15171c;
                padding: 40px;
                margin: 20px auto;
                width: 560px;
                max-width: 90%;
                border-radius: 16px;
                border: 1px solid rgba(255,255,255,0.1);
                max-height: 90vh;
                overflow-y: auto;
            }
            .modal h4 {
                color: white;
                margin-bottom: 30px;
                font-family: 'Roboto', serif;
                font-size: 32px;
                font-weight: 500;
            }
            .modal input {
                margin-bottom: 25px;
            }
            .modal input:last-of-type {
                margin-bottom: 40px;
            }
            .modal-buttons {
                display: flex;
                gap: 20px;
            }
            .modal-buttons button {
                flex: 1;
            }
            .settings-section {
                margin-top: 40px;
                padding-top: 30px;
                border-top: 1px solid rgba(255,255,255,0.1);
            }
            .setting-item {
                margin-bottom: 25px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .setting-label {
                color: white;
                font-family: 'Roboto', serif;
                font-size: 24px;
                font-weight: 400;
            }
            .setting-control {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            .setting-control input[type="checkbox"] {
                width: auto;
                margin: 0;
                transform: scale(1.5);
            }
            .setting-control input[type="range"] {
                width: 120px;
                margin: 0;
                padding: 0;
                height: 8px;
                background: rgba(255,255,255,0.2);
                border-radius: 4px;
            }
            .setting-control input[type="range"]::-webkit-slider-thumb {
                appearance: none;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #237bff;
                cursor: pointer;
            }
            .opacity-value {
                color: #ccc;
                font-size: 20px;
                min-width: 40px;
            }
            .reset-btn {
                padding: 12px 20px !important;
                font-size: 20px !important;
                width: auto !important;
                background-color: rgba(255, 193, 7, 0.15) !important;
                color: #ffc107 !important;
            }
            .reset-btn:hover {
                background-color: rgba(255, 193, 7, 0.25) !important;
            }
        </style>
    </head>
    <body>
        <button id="requestPermissions">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="48"
                height="48"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="3"
                stroke-linecap="round"
                stroke-linejoin="round"
            >
                <path
                    d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"
                />
                <path d="M12 8v4" />
                <path d="M12 16h.01" />
            </svg>
            <span>Enable Offer Tracking</span>
        </button>

        <div id="monitorSection" style="margin-top: 40px;">
            <h3>监控任务</h3>
            <div id="taskList"></div>
            <button id="addTaskBtn" style="margin-top: 20px;">添加监控任务</button>
        </div>

        <div id="globalSettings" class="settings-section">
            <h3>全局设置</h3>

            <div class="setting-item">
                <span class="setting-label">显示全局监控状态</span>
                <div class="setting-control">
                    <input type="checkbox" id="enableGlobalOverlay" />
                </div>
            </div>

            <div class="setting-item">
                <span class="setting-label">悬浮窗透明度</span>
                <div class="setting-control">
                    <input type="range" id="overlayOpacity" min="0.3" max="1" step="0.1" value="0.95" />
                    <span class="opacity-value" id="opacityValue">95%</span>
                </div>
            </div>

            <div class="setting-item">
                <span class="setting-label">悬浮窗位置</span>
                <div class="setting-control">
                    <button id="resetOverlayPosition" class="reset-btn">重置位置</button>
                </div>
            </div>
        </div>

        <div id="addTaskModal" class="modal">
            <div class="modal-content">
                <h4>添加监控任务</h4>
                <input id="taskName" placeholder="任务名称" />
                <input id="marketHashName" placeholder="Market Hash Name (如: AK-47 | Redline (Field-Tested))" />
                <input id="maxPrice" type="number" placeholder="最高价格 (分)" />
                <input id="maxFloat" type="number" step="0.001" placeholder="最高磨损值" />
                <input id="quantity" type="number" placeholder="购买数量" />
                <div class="modal-buttons">
                    <button id="saveTaskBtn">保存</button>
                    <button id="cancelTaskBtn" class="btn-inactive">取消</button>
                </div>
            </div>
        </div>

        <script src="popup/popup.js"></script>
    </body>
</html>
