/**
 * Types for the monitoring system
 */

export interface MonitorTask {
    id: string;
    name: string;
    marketHashName: string;
    maxPrice: number;
    maxFloat: number;
    quantity: number;
    isActive: boolean;
    createdAt: number;
    lastTriggered?: number;
}

export interface MonitorStatus {
    isActive: boolean;
    currentTask?: string;
    lastRefresh: number;
    totalChecked: number;
    totalTriggered: number;
    // 全局状态管理字段
    currentPage?: string;
    activeTasksCount?: number;
    recentMatches?: Array<{
        taskName: string;
        itemName: string;
        price: number;
        float: number;
        timestamp: number;
    }>;
    lastGlobalUpdate?: number;
}
