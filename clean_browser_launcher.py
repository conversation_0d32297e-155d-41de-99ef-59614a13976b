#!/usr/bin/env python3
"""
干净浏览器启动器 - 每次都是全新环境
确保每次启动都没有任何 cookies、session、缓存等信息

特点：
- 每次创建全新的用户数据目录
- 禁用所有可能的缓存和存储
- 无痕模式启动
- 自动清理临时文件

使用方法:
python clean_browser_launcher.py
"""

import os
import sys
import tempfile
import uuid
import shutil
from pathlib import Path
from playwright.sync_api import sync_playwright


def get_extension_path():
    """获取插件目录的绝对路径"""
    script_dir = Path(__file__).parent.absolute()
    extension_path = script_dir / "dist"
    
    if not extension_path.exists():
        raise FileNotFoundError(f"插件目录不存在: {extension_path}")
    
    manifest_path = extension_path / "manifest.json"
    if not manifest_path.exists():
        raise FileNotFoundError(f"manifest.json 文件不存在: {manifest_path}")
    
    return str(extension_path)


def create_clean_environment():
    """创建完全干净的浏览器环境"""
    # 使用 UUID 确保目录名完全唯一
    unique_id = str(uuid.uuid4())
    timestamp = str(int(__import__('time').time()))
    
    # 创建临时目录
    temp_dir = tempfile.gettempdir()
    user_data_dir = os.path.join(temp_dir, f"playwright_clean_{timestamp}_{unique_id}")
    
    # 确保目录不存在（额外保险）
    if os.path.exists(user_data_dir):
        shutil.rmtree(user_data_dir)
    
    os.makedirs(user_data_dir, exist_ok=True)
    
    print(f"🆕 创建全新环境: {user_data_dir}")
    print("🧹 保证：无 cookies、无 session、无缓存、无历史记录")
    
    return user_data_dir


def get_clean_browser_args(extension_path):
    """获取确保干净环境的浏览器参数"""
    return [
        # 插件相关
        f"--load-extension={extension_path}",
        f"--disable-extensions-except={extension_path}",
        
        # 安全和兼容性
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--no-sandbox",
        
        # 确保干净环境的关键参数
        "--incognito",  # 无痕模式
        "--no-first-run",  # 跳过首次运行
        "--disable-default-apps",  # 禁用默认应用
        "--disable-sync",  # 禁用同步
        "--disable-background-networking",  # 禁用后台网络
        "--disable-component-update",  # 禁用组件更新
        
        # 禁用各种缓存
        "--disable-application-cache",  # 禁用应用缓存
        "--disable-gpu-process-crash-limit",  # 禁用 GPU 进程崩溃限制
        "--disable-background-timer-throttling",  # 禁用后台定时器限制
        "--disable-backgrounding-occluded-windows",  # 禁用后台窗口
        "--disable-renderer-backgrounding",  # 禁用渲染器后台
        
        # 禁用自动功能
        "--disable-features=TranslateUI",  # 禁用翻译
        "--disable-ipc-flooding-protection",  # 禁用 IPC 洪水保护
        "--disable-blink-features=AutomationControlled",  # 避免被检测为自动化
        
        # 存储相关
        "--disable-local-storage",  # 禁用本地存储（可选）
        "--disable-databases",  # 禁用数据库（可选）
        
        # 网络相关
        "--disable-background-downloads",  # 禁用后台下载
        "--disable-client-side-phishing-detection",  # 禁用钓鱼检测
        
        # 其他
        "--disable-dev-shm-usage",  # 禁用 /dev/shm 使用
        "--disable-gpu",  # 禁用 GPU（可选，提高兼容性）
    ]


def launch_clean_browser():
    """启动完全干净的浏览器"""
    try:
        extension_path = get_extension_path()
        user_data_dir = create_clean_environment()
        
        print(f"🔧 插件路径: {extension_path}")
        
        with sync_playwright() as p:
            print("🚀 启动完全干净的浏览器...")
            
            try:
                # 获取干净环境参数
                args = get_clean_browser_args(extension_path)
                
                # 启动浏览器
                context = p.chromium.launch_persistent_context(
                    user_data_dir,
                    headless=False,
                    channel="chromium",
                    args=args
                )
                
                print("✅ 浏览器启动成功")
                print("🔒 环境状态：完全干净，无任何历史数据")
                
                # 等待插件加载
                print("⏳ 等待插件加载...")
                import time
                time.sleep(3)
                
                # 检查插件状态
                if len(context.service_workers) > 0:
                    print(f"✅ 检测到 {len(context.service_workers)} 个 service worker")
                else:
                    print("⏳ 等待 service worker 启动...")
                    try:
                        service_worker = context.wait_for_event('serviceworker', timeout=10000)
                        print("✅ Service worker 已启动")
                    except:
                        print("⚠️ Service worker 未检测到，但插件可能仍然工作")
                
                # 创建新页面
                page = context.new_page()
                
                print("\n🌐 浏览器已准备就绪")
                print("插件名称: CSFloat Market Checker")
                print("推荐测试页面:")
                print("- https://steamcommunity.com/market/listings/730/")
                print("- https://steamcommunity.com/id/[用户名]/inventory/")
                
                # 可选：自动打开测试页面
                test_url = "https://steamcommunity.com/market/listings/730/AK-47%20%7C%20Redline%20%28Field-Tested%29"
                print(f"\n🎯 正在打开测试页面: {test_url}")
                
                try:
                    page.goto(test_url, timeout=30000)
                    print("✅ 测试页面加载成功")
                except Exception as e:
                    print(f"⚠️ 测试页面加载失败: {e}")
                    print("您可以手动导航到任何页面测试插件")
                
                # 保持浏览器打开
                print("\n💡 浏览器将保持打开状态...")
                print("🔄 每次重新运行此脚本都会创建全新的干净环境")
                print("按 Enter 键关闭浏览器...")
                
                try:
                    input()
                except KeyboardInterrupt:
                    print("\n正在关闭浏览器...")
                
                # 关闭浏览器
                context.close()
                print("✅ 浏览器已关闭")
                
            except Exception as browser_error:
                print(f"❌ 浏览器启动失败: {browser_error}")
                if "Executable doesn't exist" in str(browser_error):
                    print("\n解决方案:")
                    print("1. 运行: python fix_playwright.py")
                    print("2. 或手动执行: playwright install")
                raise
            
            finally:
                # 清理临时目录
                print("🧹 清理临时文件...")
                try:
                    shutil.rmtree(user_data_dir)
                    print("✅ 临时文件清理完成")
                except Exception as e:
                    print(f"⚠️ 临时目录清理失败: {e}")
                    print(f"请手动删除: {user_data_dir}")
                
    except FileNotFoundError as e:
        print(f"❌ 错误: {e}")
        print("请确保在项目根目录运行此脚本，且 dist 目录存在")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


def check_dependencies():
    """检查依赖是否安装"""
    try:
        import playwright
        print("✅ Playwright 已安装")
        return True
    except ImportError:
        print("❌ Playwright 未安装")
        print("请运行: pip install playwright && playwright install")
        return False


if __name__ == "__main__":
    print("CSFloat Market Checker - 干净浏览器启动器")
    print("=" * 60)
    print("🎯 特点：每次启动都是全新环境，无任何历史数据")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 启动干净浏览器
    launch_clean_browser()
